# Sophisticated Workflow Structure

## Overview

The release process now uses a **reusable workflow pattern** with clear separation of concerns:

1. **`prepare-for-deployment.yml`** - Reusable workflow for any environment
2. **`prepare-staging-release.yml`** - Handles staging deployments 
3. **`publish-new-release.yml`** - Handles production deployments + GitHub releases

## Workflow Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                prepare-for-deployment.yml                   │
│                   (Reusable Workflow)                       │
│                                                             │
│  Inputs: environment, img_tag, build_sha                   │
│  Jobs: build-and-push-image → create-devops-pr             │
│  Output: deployment_completed                               │
└─────────────────────────────────────────────────────────────┘
                              ▲
                              │ (called by)
                    ┌─────────┴─────────┐
                    │                   │
┌───────────────────▼─────┐   ┌─────────▼──────────────────┐
│ prepare-staging-release │   │   publish-new-release      │
│                         │   │                            │
│ Trigger: PR events      │   │ Trigger: PR closed+merged  │
│ Environment: staging    │   │ Environment: prod          │
│ SHA: PR head            │   │ SHA: merge commit          │
└─────────────────────────┘   └────────────────────────────┘
```

## File Details

### 1. `prepare-for-deployment.yml` (Reusable)
**Purpose**: Generic deployment workflow for any environment

**Inputs**:
- `environment`: "staging" or "prod"
- `img_tag`: Docker image tag (e.g., "staging-1.2.1" or "1.2.1")
- `build_sha`: Git SHA to build from

**Jobs**:
1. `build-and-push-image` - Builds and pushes Docker image
2. `create-devops-pr` - Creates PR in devops repo to update environment

**Output**: `deployment_completed` (boolean)

### 2. `prepare-staging-release.yml` (New)
**Purpose**: Handles staging deployments for release PRs

**Trigger**: PR events (opened, reopened, synchronize) on release branches

**Flow**:
1. `generate-staging-params` - Creates staging parameters:
   - `img_tag`: "staging-{version}" (e.g., "staging-1.2.1")
   - `build_sha`: PR head SHA
2. `prepare-staging-deployment` - Calls reusable workflow with staging params

### 3. `publish-new-release.yml` (Updated)
**Purpose**: Handles production deployment + GitHub release creation

**Trigger**: PR closed+merged on release branches

**Flow**:
1. `generate-production-params` - Creates production parameters:
   - `img_tag`: "{version}" (e.g., "1.2.1")
   - `build_sha`: Merge commit SHA
2. `prepare-production-deployment` - Calls reusable workflow with prod params
3. `release` - Creates GitHub release (waits for deployment completion)

## Benefits

### ✅ **Reusability**
- Single deployment workflow used for both staging and production
- Easy to add new environments (dev, qa, etc.)

### ✅ **Clear Separation**
- Staging logic isolated in its own workflow
- Production logic combined with release publication
- Deployment logic centralized and reusable

### ✅ **Proper Dependencies**
- No more sleep-based waiting
- Clear job dependencies ensure correct execution order
- Production deployment completes before GitHub release

### ✅ **Maintainability**
- Changes to deployment logic only need to be made in one place
- Each workflow has a single, clear responsibility
- Easy to test individual components

## Execution Flow

### Staging Flow (PR Events)
```
PR opened/updated on release branch
    ↓
prepare-staging-release.yml triggers
    ↓
generate-staging-params (staging-1.2.1, PR head SHA)
    ↓
prepare-for-deployment.yml (environment: staging)
    ↓
build-and-push-image → create-devops-pr
    ↓
Staging deployment ready
```

### Production Flow (PR Merged)
```
Release PR merged to master
    ↓
publish-new-release.yml triggers
    ↓
generate-production-params (1.2.1, merge commit SHA)
    ↓
prepare-for-deployment.yml (environment: prod)
    ↓
build-and-push-image → create-devops-pr
    ↓
Production deployment completes
    ↓
GitHub release created
```

## Key Improvements

1. **Correct SHA Usage**: Production uses merge commit SHA, staging uses PR head SHA
2. **No Race Conditions**: Proper job dependencies eliminate timing issues
3. **Reusable Components**: Single deployment workflow for all environments
4. **Clear Triggers**: Staging on PR events, production on merge
5. **Maintainable**: Changes to deployment logic centralized in one file

This structure provides a robust, maintainable, and scalable release process.
