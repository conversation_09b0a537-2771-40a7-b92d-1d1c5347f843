# Auth Service

Auth Service

**Version:** 1.0.0

[Repository](https://gitlab.com/Sun/blendo-auth)

### /signup

---

##### **_POST_**

**Summary:** Create a new user

**Description:**

**Parameters**

| Name | Located in | Description | Required | Schema        |
| ---- | ---------- | ----------- | -------- | ------------- |
| user | body       |             | Yes      | [User](#user) |

**Responses**

| Code | Description          | Schema                          |
| ---- | -------------------- | ------------------------------- |
| 200  | successful operation | [UserResponse](#userresponse)   |
| 400  | failed operation     | [ErrorResponse](#errorresponse) |

### /accounts

---

##### **_POST_**

**Summary:** Create a new account

**Description:**

**Parameters**

| Name    | Located in | Description | Required | Schema              |
| ------- | ---------- | ----------- | -------- | ------------------- |
| account | body       |             | Yes      | [Account](#account) |

**Responses**

| Code | Description          | Schema                              |
| ---- | -------------------- | ----------------------------------- |
| 200  | successful operation | [AccountResponse](#accountresponse) |
| 400  | failed operation     | [ErrorResponse](#errorresponse)     |

### /panoply

---

##### **_POST_**

**Summary:** Create a new panoply account

**Description:**

**Parameters**

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ------ |
| body | body       |             | Yes      | object |

**Responses**

| Code | Description          | Schema                                            |
| ---- | -------------------- | ------------------------------------------------- |
| 200  | successful operation | [PanoplyAccountResponse](#panoplyaccountresponse) |
| 400  | failed operation     | [ErrorResponse](#errorresponse)                   |

### Models

---

### User

| Name        | Type   | Description | Required |
| ----------- | ------ | ----------- | -------- |
| email       | string |             | Yes      |
| password    | string |             | Yes      |
| name        | string |             | No       |
| company     | string |             | No       |
| created_by  | string |             | No       |
| created_via | string |             | No       |

### UserResponse

| Name        | Type   | Description | Required |
| ----------- | ------ | ----------- | -------- |
| id          | string |             | No       |
| name        | string |             | No       |
| company     | string |             | No       |
| created_at  | string |             | No       |
| created_by  | string |             | No       |
| created_via | string |             | No       |
| token       | string |             | No       |

### ErrorResponse

| Name    | Type   | Description                          | Required |
| ------- | ------ | ------------------------------------ | -------- |
| message | string | A detailed description of the error  | No       |
| status  | string | The http status                      | No       |
| code    | string | A single word that defines the error | No       |

### Account

| Name        | Type   | Description                                   | Required |
| ----------- | ------ | --------------------------------------------- | -------- |
| user_id     | string | The id of the user                            | No       |
| role        | string | The account role                              | No       |
| name        | string | The account name                              | No       |
| credentials | object | Any required info to authenticate the account | No       |

### AccountResponse

| Name       | Type   | Description           | Required |
| ---------- | ------ | --------------------- | -------- |
| id         | string | The account unique id | No       |
| user_id    | string | The id of the user    | No       |
| role       | string | The account role      | No       |
| name       | string | The account name      | No       |
| created_at | string | The creation date     | No       |
| updated_at | string | The last update date  | No       |

### PanoplyAccountResponse

| Name     | Type   | Description                       | Required |
| -------- | ------ | --------------------------------- | -------- |
| id       | string | Panoply account unique id         | No       |
| email    | string | Panoply account email             | No       |
| username | string | Panoply database username         | No       |
| password | string | Panoply database/account password | No       |
| host     | string | Panoply database host             | No       |
| port     | string | Panoply database port             | No       |
| db       | string | Panoply database name             | No       |
