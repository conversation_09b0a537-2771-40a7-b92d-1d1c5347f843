// declare namespace Express {
//   export interface Request {
//     newRoute?: boolean;
//     rudderCategory?:RudderCategory;
//   }

import { RudderScope } from 'auth';
import { RudderCategory } from '../src/lib/constants';

// }
declare module 'express-serve-static-core' {
  interface Request {
    // Indicates if a request has come from the new route
    newRoute?: boolean;
    // provides data about the rudder category from which the request has actually come from
    rudderCategory?: RudderCategory;
    // configuration required for oauth
    config?: Record<string, any>;
    // flow can be delivery or delete
    flow?: RudderScope;
  }
}
