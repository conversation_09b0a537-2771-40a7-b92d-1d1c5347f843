import { RudderCategory } from '../src/lib/constants';

export type TSPassportCallback = (error: any, response: any) => any;

export interface TSOAuthStrategyOptions {
  clientID: string;
  clientSecret: string;
  callbackURL: string;
}

export interface TSOAuthStrategyConsumerOptions {
  consumerKey: string;
  consumerSecret: string;
  callbackURL: string;
  skipExtendedUserProfile?: string;
}

export interface AuthSetupOptions {
  authenticateOptions?: Map<RudderCategory, any>;
  role: string;
  /**
   * To indicate if we need to support the legacy way of invoking OAuth related things
   * If a destination or source requires legacy support, we need to set this property to `true` explicitly
   *
   * __Short Background__:
   * Legacy calls(without rudderCategory in url) needs to be supported for backward compatibility
   * Initially this was the norm being employed to make calls to `/authz` or `/callback` or `/token`
   * But as part of evolution, we are now using a category for these calls
   * so that we can easily handle the use-cases for each of the categories
   *
   * Latest url for /authz look like this: `/<category>/<destination>/authz`
   * Legacy url for /authz look like this: `/<destination>/authz`
   *
   */
  supportLegacy?: boolean;
}

export type SalesforceRoles =
  | 'salesforce'
  | 'salesforce_sandbox'
  | 'singer-salesforce'
  | 'singer-salesforce-sandbox';

export type RudderScope = 'delivery' | 'delete';

export type RefreshTokenMethodOptions = { flow?: RudderScope; config?: Record<string, any> };

export type RefreshTokenMethod = (
  body: any,
  rudderCategory?: RudderCategory,
  options?: RefreshTokenMethodOptions,
) => Promise<any>;
