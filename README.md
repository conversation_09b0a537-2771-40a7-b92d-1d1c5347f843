# Auth Service

Auth Service

- [endpoint](#Endpoints)
- [configuration](#configuration)
- [development](#development)

**Version:** 1.0.0

# Oauth Endpoints

[GET] /auth/:role/callback
[GET] /auth/:role/authz

# Endpoints

### /tokens/:role/refresh

---

##### **_POST_**

**Description:** Returns a refreshed access_token. Currently supports xero, google, quickbooks, salesforce roles.
</br>
</br>

**Parameters**

| Name         | Located in | Description       | Required |
| ------------ | ---------- | ----------------- | -------- |
| refreshToken | body       | The refresh token | Yes      |

**Responses**

| Code     | Description          | Schema                          |
| -------- | -------------------- | ------------------------------- |
| 200      | successful operation | [TokenResponse](#tokenResponse) |
| 4xx, 5xx | operation error      | [ErrorResponse](#errorResponse) |

</br>
</br>

### TokenResponse

| Name           | Type      | Required |
| -------------- | --------- | -------- |
| access_token   | string    | Yes      |
| refresh_token  | string    | Yes      |
| expirationDate | isoString | No       |

</br>
</br>

### ErrorResponse

| Name    | Type   | Required |
| ------- | ------ | -------- |
| message | string | No       |
| status  | number | No       |
| code    | string | No       |

---

# Development

**Scripts**

```bash

# runs dev server with watch
npm run start-develop

# builds the project
npm run build

# starts the builded server
yarn start

```
