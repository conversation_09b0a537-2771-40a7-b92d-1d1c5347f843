FROM node:20.19.2-alpine3.20

RUN apk update
RUN apk upgrade

# installing specific python version based on your previous configuration
# RUN apk add python2 

# installing specific make version based on your previous configuration
RUN apk add --no-cache make

# installing specific gcc version based on your previous configuration
RUN apk add --no-cache g++

RUN mkdir /app
ADD . /app
WORKDIR /app

EXPOSE 3033
RUN npm install
RUN npm run build
# Ensure swagger.yaml is in the right place
RUN cp swagger.yaml build/

RUN mkdir -p /usr/src/config
RUN chmod +x ./docker/startup.sh
ENV NODE_ENV production
CMD [ "./docker/startup.sh" ]
