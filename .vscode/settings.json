{"prettier.requireConfig": true, "prettier.configPath": ".prettier<PERSON>", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[yaml]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "editor.codeActionsOnSave": {"source.organizeImports": "never"}, "eslint.validate": ["javascript", "typescript"]}