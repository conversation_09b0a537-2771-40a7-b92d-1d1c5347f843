# Simple GitHub Workflows Fixes

## Issues Fixed

### 1. **Critical SHA Issue** ❌➡️✅
**Problem**: Production images were built using wrong SHA (base branch instead of merge commit)

**Fix**: Changed from `github.event.pull_request.base.sha` to `github.event.pull_request.merge_commit_sha`

**File**: `prepare-for-deployment.yml` line 50

### 2. **Workflow Dependency Issue** ❌➡️✅
**Problem**: `publish-new-release.yml` used unreliable sleep-based waiting for deployment

**Fix**: 
- Made `prepare-for-deployment.yml` reusable with `workflow_call`
- Added proper job dependency in `publish-new-release.yml`
- `publish-new-release.yml` now waits for `prepare-deployment` job to complete

**Files**: Both `prepare-for-deployment.yml` and `publish-new-release.yml`

### 3. **Concurrency Issue** ❌➡️✅
**Problem**: Staging workflows could be cancelled prematurely

**Fix**: 
- Changed `cancel-in-progress: false`
- Added action type to concurrency group for better isolation

**File**: `prepare-for-deployment.yml` lines 22-24

## Changes Made

### `prepare-for-deployment.yml`
```yaml
# Added workflow_call trigger
on:
  workflow_call:
    outputs:
      deployment_completed:
        value: ${{ jobs.create-devops-pr.outputs.deployment_completed }}

# Fixed SHA selection
build_sha="${{ github.event.pull_request.merge_commit_sha }}"

# Fixed concurrency
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.sha }}-${{ github.event.action }}
  cancel-in-progress: false

# Added completion tracking
- name: Mark deployment as completed
  id: deployment-status
  run: echo "completed=true" >> $GITHUB_OUTPUT
```

### `publish-new-release.yml`
```yaml
jobs:
  prepare-deployment:
    if: (release conditions)
    uses: ./.github/workflows/prepare-for-deployment.yml

  release:
    needs: prepare-deployment  # Proper dependency
    if: (release conditions)
    # ... rest of release steps
```

## Result

1. **Production images now built from correct commit** (merge commit SHA)
2. **Reliable workflow dependency** (no more sleep-based waiting)
3. **Staging workflows won't be cancelled** inappropriately
4. **Clear execution order**: deployment → release publication

## Testing

1. Create a test release PR
2. Verify staging deployment triggers on PR events
3. Merge the PR and verify production deployment completes before release publication
4. Confirm GitHub release is created only after deployment finishes

These minimal changes fix the core issues without over-engineering the solution.
