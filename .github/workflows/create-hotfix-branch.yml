name: <PERSON>reate New Hotfix Branch

on:
  workflow_dispatch:
    inputs:
      hotfix_name:
        description: Hotfix branch name (e.g., temp -> hotfix/temp)
        required: true

jobs:
  create-branch:
    name: Create New Hotfix Branch
    runs-on: ubuntu-latest

    steps:
      # For cross team releases, we will avoid this check
      # - name: Check User Membership
      #   uses: tspascoal/get-user-teams-membership@v3
      #   id: checkUserMember
      #   with:
      #     username: ${{ github.actor }}
      #     team: 'integrations_team'
      #     GITHUB_TOKEN: ${{ secrets.PAT }}
      - name: Create Branch
        uses: peterjgrainger/action-create-branch@v2.4.0
        env:
          GITHUB_TOKEN: ${{ secrets.PAT }}
        with:
          branch: 'hotfix/${{ inputs.hotfix_name }}'
