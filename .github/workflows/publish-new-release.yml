name: Publish New GitHub Release

on:
  pull_request:
    types:
      - closed
    branches:
      - master

jobs:
  prepare-deployment:
    name: Prepare Deployment
    if: (startsWith(github.event.pull_request.head.ref, 'release/') || startsWith(github.event.pull_request.head.ref, 'hotfix-release/')) && github.event.pull_request.merged == true
    uses: ./.github/workflows/prepare-for-deployment.yml

  release:
    name: Publish New GitHub Release
    runs-on: ubuntu-latest
    needs: prepare-deployment
    if: (startsWith(github.event.pull_request.head.ref, 'release/') || startsWith(github.event.pull_request.head.ref, 'hotfix-release/')) && github.event.pull_request.merged == true

    steps:
      - name: Extract Version
        id: extract-version
        run: |
          branch_name="${{ github.event.pull_request.head.ref }}"
          version=${branch_name#*/}
          echo "release_version=$version" >> $GITHUB_OUTPUT

      - name: Checkout
        uses: actions/checkout@v4.2.1
        with:
          fetch-depth: 0

      - name: Initialize Mandatory Git Config
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.PAT }}
        run: |
          gh release create v${{ steps.extract-version.outputs.release_version}} \
            --title "v${{ steps.extract-version.outputs.release_version }}" \
            --notes-file RELEASE_NOTES.md

      - name: Pull Changes Into develop Branch
        run: |
          gh pr create \
            --base develop \
            --head master \
            --title "chore(release): pull master into develop post release v${{ steps.extract-version.outputs.release_version }}" \
            --body ":crown: *An automated PR*"
        env:
          GH_TOKEN: ${{ secrets.PAT }}

      - name: Delete Release or Hotfix Branch
        run: |
          branch_name="${{ github.event.pull_request.head.ref }}"
          git push origin --delete "$branch_name"

        env:
          GITHUB_TOKEN: ${{ secrets.PAT }}

      - name: Notify Slack Channel
        id: slack
        uses: slackapi/slack-github-action@v1.27.0
        continue-on-error: true
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
          PROJECT_NAME: 'Rudder Auth'
          RELEASES_URL: 'https://github.com/rudderlabs/rudder-auth/releases/tag/'
        with:
          channel-id: ${{ secrets.SLACK_RELEASE_CHANNEL_ID }}
          payload: |
            {
              "text": "*<${{env.RELEASES_URL}}v${{ steps.extract-version.outputs.release_version }}|v${{ steps.extract-version.outputs.release_version }}>*\nCC: <@U03KG4BK1L1> <@U024YF8CR53> <@U01LVJ30QEB>",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":tada:  ${{ env.PROJECT_NAME }} - New GitHub Release  :tada:"
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*<${{env.RELEASES_URL}}v${{ steps.extract-version.outputs.release_version }}|v${{ steps.extract-version.outputs.release_version }}>*\nCC: <@U03KG4BK1L1> <@U024YF8CR53> <@U01LVJ30QEB>"
                  }
                }
              ]
            }
