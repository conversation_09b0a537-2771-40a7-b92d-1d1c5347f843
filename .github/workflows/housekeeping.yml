name: Handle stale PRs

on:
  schedule:
    - cron: '42 1 * * *'

jobs:
  prs:
    name: Clean up stale PRs
    runs-on: ubuntu-latest

    permissions:
      pull-requests: write

    steps:
      - uses: actions/stale@v5
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          operations-per-run: 200
          stale-pr-message: 'This PR is considered to be stale. It has been open 20 days with no further activity thus it is going to be closed in 7 days. To avoid such a case please consider removing the stale label manually or add a comment to the PR.'
          days-before-pr-stale: 20
          days-before-pr-close: 7
          stale-pr-label: 'Stale'

  branches:
    name: Cleanup old branches
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Run delete-old-branches-action
        uses: beatlabs/delete-old-branches-action@v0.0.9
        with:
          repo_token: ${{ github.token }}
          date: '6 months ago'
          dry_run: false
          delete_tags: false
          extra_protected_branch_regex: ^(develop|master)$
          exclude_open_pr_branches: true
