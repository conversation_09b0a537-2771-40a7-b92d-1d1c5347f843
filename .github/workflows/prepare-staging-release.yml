name: Prepare Staging Release

on:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
    branches:
      - master

permissions:
  id-token: write
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.sha }}
  cancel-in-progress: false

jobs:
  generate-staging-params:
    runs-on: ubuntu-latest
    name: Generate Staging Parameters
    # Only pull requests from release candidate branches must trigger
    if: (startsWith(github.event.pull_request.head.ref, 'release/') || startsWith(github.event.pull_request.head.ref, 'hotfix-release/'))
    outputs:
      img_tag: ${{ steps.gen_params.outputs.img_tag }}
      build_sha: ${{ steps.gen_params.outputs.build_sha }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4.2.1
        with:
          fetch-depth: 1

      - name: Generate Staging Parameters
        id: gen_params
        run: |
          base_tag="$(jq -r .version package.json)"
          img_tag="staging-${base_tag}"
          build_sha="${{ github.event.pull_request.head.sha }}"
          
          echo "Staging Image Tag: $img_tag"
          echo "Build SHA: $build_sha"
          
          echo "img_tag=$img_tag" >> $GITHUB_OUTPUT
          echo "build_sha=$build_sha" >> $GITHUB_OUTPUT

  prepare-staging-deployment:
    name: Prepare Staging Deployment
    needs: generate-staging-params
    uses: ./.github/workflows/prepare-for-deployment.yml
    with:
      environment: "staging"
      img_tag: ${{ needs.generate-staging-params.outputs.img_tag }}
      build_sha: ${{ needs.generate-staging-params.outputs.build_sha }}
    secrets: inherit
