name: Draft Release

on:
  workflow_dispatch:

jobs:
  draft-release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4.2.1
        with:
          fetch-depth: 0

      - name: Set up Node.js
        uses: actions/setup-node@v4.0.4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Install Dependencies
        env:
          HUSKY: 0
        run: |
          npm ci

      - name: Configure Git
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Get Next Version and Branch Name
        id: next-version
        run: |
          NEXT_VERSION=$(npx semantic-release --dry-run --no-ci --plugin '@semantic-release/commit-analyzer' | grep "next release version is" | awk '{print $NF}')
          echo "version=$NEXT_VERSION" >> $GITHUB_OUTPUT
          if [[ "${{ startsWith(github.ref, 'refs/heads/hotfix/') }}" == "true" ]]; then
            echo "branch_name=hotfix-release/$NEXT_VERSION" >> $GITHUB_OUTPUT
          else
            echo "branch_name=release/$NEXT_VERSION" >> $GITHUB_OUTPUT
          fi

      - name: Create Release Branch
        run: |
          git checkout -b "${{ steps.next-version.outputs.branch_name }}"
          git push --set-upstream origin "${{ steps.next-version.outputs.branch_name }}"

      - name: Update Changelog & Bump Version
        run: |
          # Update changelog
          npx semantic-release --no-ci
          # Bump version
          npm version --no-git-tag-version ${{ steps.next-version.outputs.version }}
          # Commit and push
          git add .
          git commit -m "chore(release): prepare release ${{ steps.next-version.outputs.version }}"
          git push

      - name: Create Pull Request
        run: |
          gh pr create \
            --base master \
            --head "${{ steps.next-version.outputs.branch_name }}" \
            --title "chore(release): pull ${{ steps.next-version.outputs.branch_name }} into master" \
            --body ":crown: *An automated PR*"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
