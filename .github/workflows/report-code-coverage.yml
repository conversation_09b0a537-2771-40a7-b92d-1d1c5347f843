name: Report Code Coverage
on:
  workflow_call:
  pull_request:
    types: ['opened', 'reopened', 'synchronize']

jobs:
  coverage:
    name: Code Coverage
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3.5.3
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 1

      - name: Setup Node
        uses: actions/setup-node@v3.7.0
        with:
          node-version-file: .nvmrc
          cache: 'npm'

      - name: Install Dependencies
        run: npm ci

      - name: Run Tests & Coverage
        run: |
          npm run test-coverage

      - name: Upload Coverage Reports to Codecov
        uses: codecov/codecov-action@v3.1.4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./reports/coverage
