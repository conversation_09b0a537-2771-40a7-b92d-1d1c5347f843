name: Build Docker Image

on:
  workflow_call:
    inputs:
      img_tag:
        required: true
        type: string
      ref:
        required: true
        type: string
    secrets:
      DOCKERHUB_USERNAME:
        required: true
      DOCKERHUB_TOKEN:
        required: true

permissions:
  id-token: write
  contents: read

jobs:
  build-rudder-auth-image:
    name: Build Docker Image
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4.1.0
        with:
          ref: ${{ inputs.ref }}
          fetch-depth: 1

      - name: Login to DockerHub
        uses: docker/login-action@v3.3.0
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3.7.1

      - name: Build and Push Images
        uses: rudderlabs/build-scan-push-action@v1.5.2
        with:
          context: .
          file: Dockerfile
          push: true
          platforms: linux/amd64
          tags: rudderstack/rudder-auth:${{ inputs.img_tag }}
