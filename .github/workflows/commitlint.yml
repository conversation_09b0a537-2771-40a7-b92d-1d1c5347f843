name: Commitlint

on: [push]

jobs:
  commitlint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4.1.1
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v4.0.1
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Install Dependencies
        run: npm ci

      - name: Print versions
        run: |
          git --version
          node --version
          npm --version
          npx commitlint --version

      # Run the commitlint action, considering its own dependencies and yours as well 🚀
      # `github.workspace` is the path to your repository.
      - uses: wagoid/commitlint-github-action@v5
        env:
          NODE_PATH: ${{ github.workspace }}/node_modules
        with:
          commitDepth: 1
