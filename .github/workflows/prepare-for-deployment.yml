name: Prepare for Environment Deployment

on:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - closed
    branches:
      - master
  workflow_call:
    outputs:
      deployment_completed:
        description: "Whether deployment was completed successfully"
        value: ${{ jobs.create-devops-pr.outputs.deployment_completed }}

permissions:
  id-token: write
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.sha }}-${{ github.event.action }}
  cancel-in-progress: false

jobs:
  generate-tag-names:
    runs-on: ubuntu-latest
    name: Generate Tag Names
    # Only pull requests from release candidate branches must trigger
    if: (startsWith(github.event.pull_request.head.ref, 'release/') || startsWith(github.event.pull_request.head.ref, 'hotfix-release/'))
    outputs:
      tag_name: ${{ steps.gen_tag_names.outputs.tag_name }}
      environment: ${{ steps.gen_tag_names.outputs.environment }}
      build_sha: ${{ steps.gen_tag_names.outputs.build_sha }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4.2.1
        with:
          fetch-depth: 1

      - name: Generate Tag Names
        id: gen_tag_names
        run: |
          base_tag="$(jq -r .version package.json)"
          if [[ "${{ github.event.action }}" == "closed" && "${{ github.event.pull_request.merged }}" == "true" ]]; then
            tag_name="${base_tag}"
            environment="prod"
            # For production, use the merge commit SHA
            build_sha="${{ github.event.pull_request.merge_commit_sha }}"
          else
            tag_name="staging-${base_tag}"
            environment="staging"
            # For staging, use the PR head SHA
            build_sha="${{ github.event.pull_request.head.sha }}"
          fi
          echo "Tag Name: $tag_name"
          echo "tag_name=$tag_name" >> $GITHUB_OUTPUT
          echo "environment=$environment" >> $GITHUB_OUTPUT
          echo "build_sha=$build_sha" >> $GITHUB_OUTPUT

  build-and-push-image:
    uses: ./.github/workflows/build-and-push-image.yml
    needs: generate-tag-names
    with:
      ref: ${{ needs.generate-tag-names.outputs.build_sha }}
      img_tag: ${{ needs.generate-tag-names.outputs.tag_name }}
    secrets: inherit

  create-devops-pr:
    runs-on: ubuntu-latest
    needs: [generate-tag-names, build-and-push-image]
    outputs:
      deployment_completed: ${{ steps.deployment-status.outputs.completed }}
    steps:
      - name: Initialize Mandatory Git Config
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Clone Devops Repo
        run: |
          git clone https://${{secrets.PAT}}@github.com/rudderlabs/rudder-devops.git

      - name: Raise PR to update image in ${{ needs.generate-tag-names.outputs.environment }}
        env:
          GITHUB_TOKEN: ${{ secrets.PAT }}
        run: |
          img_version=${{ needs.generate-tag-names.outputs.tag_name }}
          cd rudder-devops
          branch_name="rudder-auth-$img_version"
          git checkout -b $branch_name

          # Determine environment based on PR status
          env_path="helm-charts/auth-service/environment/${{ needs.generate-tag-names.outputs.environment }}"

          cd $env_path
          yq eval -i ".image=\"rudderstack/rudder-auth:$img_version\"" base.yaml
          git add base.yaml
          git commit -m "chore: upgrade rudder-auth to $img_version"
          git push -u origin $branch_name

          gh pr create --fill

      - name: Mark deployment as completed
        id: deployment-status
        run: |
          echo "completed=true" >> $GITHUB_OUTPUT
