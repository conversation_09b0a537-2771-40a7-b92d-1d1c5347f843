{"name": "rudder-auth", "version": "1.2.1", "private": true, "main": "./build/server.js", "scripts": {"start": "node build/src/server.js", "start-dev": "ts-node-dev --files=types/*.d.ts src/server.ts -w src -e ts,tsx", "build": "npm run clean && tsc && cp swagger.yaml build/", "clean": "rm -rf build", "test": "jest -c jest.config.js --detect<PERSON><PERSON><PERSON>andles", "test-watch": "jest --watch -c jest.config.js --detect<PERSON><PERSON><PERSON><PERSON>les", "test-silent": "jest -c jest.config.js --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --silent", "test-coverage": "jest --coverage", "pre-commit": "npx lint-staged && npm run test", "format:check": "prettier . --check", "format": "prettier . --write", "lint:fix": "eslint '**/*.ts' --fix", "lint": "npm run format && npm run lint:fix", "docker-build": "docker build -t rudderstack/rudder-auth:v1.1.4 .", "commit-msg": "commitlint --edit"}, "dependencies": {"@rudderstack/integrations-lib": "^0.2.11", "@semantic-release/changelog": "^6.0.3", "@semantic-release/exec": "^7.0.3", "@semantic-release/git": "^10.0.1", "axios": "^1.8.4", "body-parser": "^1.19.0", "commander": "^12.0.0", "config": "^3.3.11", "dotenv": "^8.2.0", "express": "^4.21.2", "express-session": "^1.17.3", "fast-xml-parser": "^4.4.1", "fb": "^2.0.0", "form-data": "^4.0.0", "get-value": "^3.0.1", "helmet": "^7.0.0", "lodash": "^4.17.21", "lossless-json": "^2.0.11", "moment": "^2.29.4", "morgan": "^1.10.0", "passport": "^0.7.0", "passport-bing-ads": "^1.0.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-hubspot-oauth2": "^1.0.3", "passport-intercom": "0.0.4", "passport-intuit-oauth": "^1.0.0", "passport-linkedin-oauth2": "^2.0.0", "passport-mailchimp": "^1.1.0", "passport-oauth2": "^1.5.0", "passport-salesforce": "0.0.2", "passport-snapchat": "^1.0.0", "passport-stripe": "^0.2.3", "passport-twitter": "^0.1.5", "passport-xero-oauth2": "0.0.3", "passport-yandex": "^0.0.5", "passport-zendesk": "0.0.2", "passport-zoho-crm": "^0.1.3", "prom-client": "^15.1.3", "querystring": "^0.2.1", "random-string": "^0.2.0", "semantic-release": "^24.2.3", "swagger-ui-express": "^5.0.1", "ts-node": "^9.1.1", "yamljs": "^0.3.0"}, "devDependencies": {"@commitlint/cli": "^18.5.0", "@commitlint/config-conventional": "^18.5.0", "@semantic-release/github": "^11.0.1", "@semantic-release/release-notes-generator": "^14.0.3", "@types/axios": "^0.14.0", "@types/body-parser": "^1.19.0", "@types/config": "3.3.4", "@types/express": "^4.17.9", "@types/fb": "^0.0.28", "@types/get-value": "^3.0.5", "@types/http-terminator": "^2.0.5", "@types/intercom-client": "^2.11.10", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^8.5.0", "@types/lodash": "^4.17.0", "@types/morgan": "^1.9.2", "@types/node": "^18.17.14", "@types/passport": "^1.0.4", "@types/passport-google-oauth20": "^2.0.14", "@types/passport-oauth2": "^1.4.11", "@types/passport-strategy": "^0.2.35", "@types/random-string": "0.0.28", "@types/set-value": "^4.0.3", "@types/supertest": "^6.0.2", "@types/swagger-ui-express": "^4.1.8", "@types/url-join": "^4.0.0", "@types/yamljs": "^0.2.34", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "axios-mock-adapter": "^1.22.0", "conventional-changelog-conventionalcommits": "^8.0.0", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-security": "^1.7.1", "esnext": "^3.3.1", "glob": "^10.3.12", "http-terminator": "^3.2.0", "husky": "^8.0.0", "lint-staged": "^15.2.0", "node-notifier": "^10.0.1", "prettier": "^3.1.1", "supertest": "^6.3.4", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "tslint": "^6.1.3", "typescript": "^4.1.3"}, "overrides": {"tough-cookie": "^4.1.3"}, "lint-staged": {"*.{ts,tsx}": "eslint --cache --fix", "*.{json,js,ts,ts,md,yml}": "prettier --write"}}