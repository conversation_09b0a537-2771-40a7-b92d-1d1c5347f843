## [1.2.1](https://github.com/rudderlabs/rudder-auth/compare/v1.2.0...v1.2.1) (2025-06-09)

### 🔧 Chores

* add ads and audiences scopes to both linkedin auth routes ([#461](https://github.com/rudderlabs/rudder-auth/issues/461)) ([e639be0](https://github.com/rudderlabs/rudder-auth/commit/e639be0560959a62070e05de6ccbc5621ea11f0f))
* **release:** prepare release 1.2.0 ([918b591](https://github.com/rudderlabs/rudder-auth/commit/918b591a771b8d05816c2d44d69d94d4749797d2))

## [1.2.0](https://github.com/rudderlabs/rudder-auth/compare/v1.1.1...v1.2.0) (2025-05-26)

### ✨ Features

* add userName as common identifier from vendor ([#446](https://github.com/rudderlabs/rudder-auth/issues/446)) ([b698251](https://github.com/rudderlabs/rudder-auth/commit/b6982511736e474568e4cd6de8569280c16f1e5e))
* address review comment ([1fa0ed1](https://github.com/rudderlabs/rudder-auth/commit/1fa0ed1b39278e9a2394e1c1e07eafec91843dfd))
* address review comments ([c7a8b89](https://github.com/rudderlabs/rudder-auth/commit/c7a8b89362786ee7df6a1710a2db07cb2316eec3))
* onboard linkedin implementation ([3486c45](https://github.com/rudderlabs/rudder-auth/commit/3486c458bae083a143324eb94ba12a496dc81dba))
* onboard static callback url for v1 implementations ([#443](https://github.com/rudderlabs/rudder-auth/issues/443)) ([8155ce9](https://github.com/rudderlabs/rudder-auth/commit/8155ce9277290d15cbdc129271444bfc6c1899af))
* onboard test cases ([cc970ba](https://github.com/rudderlabs/rudder-auth/commit/cc970ba35abfc8842d5fc7e1ae14dd175a37ae68))
* onboard v1 implementation framework with zoho ([c70053b](https://github.com/rudderlabs/rudder-auth/commit/c70053b30167ae5fed92f78d9bfcbcde3e024e4e))
* resolve conflicts ([30a1949](https://github.com/rudderlabs/rudder-auth/commit/30a1949a2755c42e4fc1afd5c7a6e5fc226bf246))
* update swagger and endpoint for url ([74bca3b](https://github.com/rudderlabs/rudder-auth/commit/74bca3b21f5a855f0f1ff49cdf995ed9b7a21e2a))

### 🔧 Chores

* add swagger ([5377c23](https://github.com/rudderlabs/rudder-auth/commit/5377c2376ad3bb2721988c00e4c7119dfee633c9))
* **deps:** bump config and @types/config ([#450](https://github.com/rudderlabs/rudder-auth/issues/450)) ([0eec45e](https://github.com/rudderlabs/rudder-auth/commit/0eec45eb46826dbfdaf8be6c908dfa5063385380))
* **release:** prepare release 1.1.0 ([0f9fef8](https://github.com/rudderlabs/rudder-auth/commit/0f9fef8cc112b8b26c5e54a0d3baf56a9f87389f))
* **release:** prepare release 1.1.1 ([c84d687](https://github.com/rudderlabs/rudder-auth/commit/c84d687aa189f8b4668c25275292827d3bee32f4))
* remove binding of config for oauth ([e04456d](https://github.com/rudderlabs/rudder-auth/commit/e04456d1ca7f989217b599e2eded08f0b7792009))
* snyk security upgrade node from 20.18.0-alpine3.20 to 20.19.2-alpine3.20 ([#447](https://github.com/rudderlabs/rudder-auth/issues/447)) ([3df8c2f](https://github.com/rudderlabs/rudder-auth/commit/3df8c2f0aa2cad7834104e93083d0398fcec6048))
* update package json ([c564c04](https://github.com/rudderlabs/rudder-auth/commit/c564c04631e32c828d627b2e492c21248bb84e7b))

## [1.1.1](https://github.com/rudderlabs/rudder-auth/compare/v1.1.0...v1.1.1) (2025-04-23)

### 🔧 Chores

* fix prettierignore and dockerignore ([560de92](https://github.com/rudderlabs/rudder-auth/commit/560de92ae81b49f13e3cc19ea436eb542c064ac4))

## [1.1.0](https://github.com/rudderlabs/rudder-auth/compare/v1.0.2...v1.1.0) (2025-04-23)

### ✨ Features

* action for develop image ([#435](https://github.com/rudderlabs/rudder-auth/issues/435)) ([dd5c796](https://github.com/rudderlabs/rudder-auth/commit/dd5c7963fb1b82c4c222bbd0c8cdfe3ac0c1144f))

### 🔧 Chores

* integrate build scan push for secret scanning ([#420](https://github.com/rudderlabs/rudder-auth/issues/420)) ([0b012ce](https://github.com/rudderlabs/rudder-auth/commit/0b012ce24cf5a2ac2b481cc13994d06817a95808))
* package json version bump and release with release_notes.md ([9d94cf3](https://github.com/rudderlabs/rudder-auth/commit/9d94cf3c9a424d1698c452dcb5cf413c34234caf))
* release notes beautification ([1110dc8](https://github.com/rudderlabs/rudder-auth/commit/1110dc87ce4563f6b55bc84110135827426bb5bb))
* release rc changes for release notes fix ([470340c](https://github.com/rudderlabs/rudder-auth/commit/470340c1aba32b1742fdfe338966ebaa42ebbedb))
* remove binding of config for oauth ([#431](https://github.com/rudderlabs/rudder-auth/issues/431)) ([d68babb](https://github.com/rudderlabs/rudder-auth/commit/d68babbe3cbb87a0fbd4b424ba8c2457bd7ca11d))
* update passport and tough cookie dependencies ([#430](https://github.com/rudderlabs/rudder-auth/issues/430)) ([45f4b57](https://github.com/rudderlabs/rudder-auth/commit/45f4b57bc8202d565c5c40c9178a62c51424e725)), closes [#216](https://github.com/rudderlabs/rudder-auth/issues/216)
* update swagger file ([#436](https://github.com/rudderlabs/rudder-auth/issues/436)) ([7b5a160](https://github.com/rudderlabs/rudder-auth/commit/7b5a160971cfc95ab266469f17a8c425e1e7ff9d))
* **workflows:** semantix release worflow fix and test rc changes ([6d4beeb](https://github.com/rudderlabs/rudder-auth/commit/6d4beeb830dae044b7977ee25c77447a9f7d8285))


## [1.0.1](https://github.com/rudderlabs/rudder-auth/compare/v1.0.0...v1.0.1) (2025-04-02)

# 1.0.0 (2025-03-11)


### Bug Fixes

* add expirationDate to bingAdsAudience ([#144](https://github.com/rudderlabs/rudder-auth/issues/144)) ([81d0d77](https://github.com/rudderlabs/rudder-auth/commit/81d0d776e291bd6b6a1d0dd0e87b0e27b10c24b4))
* add linkedin audience to invalid grant error list ([#395](https://github.com/rudderlabs/rudder-auth/issues/395)) ([da4d7a3](https://github.com/rudderlabs/rudder-auth/commit/da4d7a31ee35055852a24b41691122452ef89aef))
* add metadata in account for zendesk ([#84](https://github.com/rudderlabs/rudder-auth/issues/84)) ([a5f3a7d](https://github.com/rudderlabs/rudder-auth/commit/a5f3a7db1bf846dadfe5bf44b0f83f7ba445e80f))
* add npm package-manager to package-ecosystem ([eb789fb](https://github.com/rudderlabs/rudder-auth/commit/eb789fb95e07a3cadc672a760983b189a8f6a096))
* added scope in dcm ([3b9ce70](https://github.com/rudderlabs/rudder-auth/commit/3b9ce703727a5480a0a37d4010f7d2ff463a42ec))
* added scopes for ticket pipelines ([e453993](https://github.com/rudderlabs/rudder-auth/commit/e453993ebd233b29f3fbdebe871f10ec77b42e60))
* added scopes for ticket pipelines ([99779fb](https://github.com/rudderlabs/rudder-auth/commit/99779fb51f349a1f2f2bf7e1e2f3496e39e47771))
* bing ads conflicts ([cc80df0](https://github.com/rudderlabs/rudder-auth/commit/cc80df04cae06fbb28f69acf1fd24f3504a7a35b))
* changed hubspot scopes ([16b70d6](https://github.com/rudderlabs/rudder-auth/commit/16b70d60fa2edad473e9b31c920e861ffa98dcf5))
* checks if state exists ([b23d06b](https://github.com/rudderlabs/rudder-auth/commit/b23d06b8266b1e1d79fff1a16231993b68bbc095))
* code vulnerabilities ([#202](https://github.com/rudderlabs/rudder-auth/issues/202)) ([d0516fa](https://github.com/rudderlabs/rudder-auth/commit/d0516fa29bf9381170fcf85aa23b5d980da8f943))
* create common file for audience and offlien conversions ([ac0577b](https://github.com/rudderlabs/rudder-auth/commit/ac0577b81ff1d507ba99ae5dc98859bb53571284))
* custom variable addition for vault access ([#291](https://github.com/rudderlabs/rudder-auth/issues/291)) ([be57ce0](https://github.com/rudderlabs/rudder-auth/commit/be57ce09988f7f81e64ddd6bd6db9a7b74d50f89))
* develop action ([#142](https://github.com/rudderlabs/rudder-auth/issues/142)) ([573a777](https://github.com/rudderlabs/rudder-auth/commit/573a777f37227df49b0b73a8381d5f78eaab0f82))
* docker build ([591646b](https://github.com/rudderlabs/rudder-auth/commit/591646ba467214fdf3250e6c2eb93fcb238fc507))
* Dockerfile to reduce vulnerabilities ([500dd39](https://github.com/rudderlabs/rudder-auth/commit/500dd390248d528fa1a6e03bebc4ae03872632f8))
* enable creation of multiple account in bingads audience ([#271](https://github.com/rudderlabs/rudder-auth/issues/271)) ([5b131a5](https://github.com/rudderlabs/rudder-auth/commit/5b131a5ef75f87c2af2899601de551ea4f649648))
* fixing salesforce sandbox v2 refresh token flow ([#386](https://github.com/rudderlabs/rudder-auth/issues/386)) ([4161f29](https://github.com/rudderlabs/rudder-auth/commit/4161f29e63d5a0ed731fef71a019fa6aeba1e78a))
* google sheets scopes ([#110](https://github.com/rudderlabs/rudder-auth/issues/110)) ([be943a0](https://github.com/rudderlabs/rudder-auth/commit/be943a0dd0fc883b7b3b5ce173a059ed752da62d))
* google sheets scopes ([#110](https://github.com/rudderlabs/rudder-auth/issues/110)) ([fc04b56](https://github.com/rudderlabs/rudder-auth/commit/fc04b568e5fe262bcd038a530881e81688ca5961))
* high vulnerable dependency axios ([4fcb145](https://github.com/rudderlabs/rudder-auth/commit/4fcb1455413e21c1d5143155fb3856e81a4f88a1))
* high vulnerable dependency decode-uri-component ([572678e](https://github.com/rudderlabs/rudder-auth/commit/572678e9cd58432e31b7b2d42700d98993bddb5e))
* high vulnerable dependency follow-redirects ([5b42e38](https://github.com/rudderlabs/rudder-auth/commit/5b42e38e08bcbdf0d4704b087fc063087cb53d7c))
* high vulnerable dependency moment ([8e36ab2](https://github.com/rudderlabs/rudder-auth/commit/8e36ab20b447fa3a87b5b43ed3b44ef222e7d259))
* high vulnerable dependency qs ([c9ba677](https://github.com/rudderlabs/rudder-auth/commit/c9ba67760a6fa9e172a0e164322b84814271874d))
* high-vulnerable dependency ansi-regex ([02b6dcd](https://github.com/rudderlabs/rudder-auth/commit/02b6dcd1b01d189e5e50b37c58fbbdfc094fdfbd))
* invalid grant error handling for google related destinations ([#192](https://github.com/rudderlabs/rudder-auth/issues/192)) ([65efbe1](https://github.com/rudderlabs/rudder-auth/commit/65efbe14267a1770ae7d62a1b71775cfdf30203e))
* json-schema, minimist critical vulnerabilities fix ([367a4cb](https://github.com/rudderlabs/rudder-auth/commit/367a4cb08f42562fbe17dbd2de2042c1dafdefbd))
* lint issues ([339add0](https://github.com/rudderlabs/rudder-auth/commit/339add03282c50c07f4beacb43e657dbd9d5933e))
* lint issues+1 ([185e93e](https://github.com/rudderlabs/rudder-auth/commit/185e93e1a864befe70f25b8af6e752d6470575d0))
* metrics with default labels, show node metrics ([#385](https://github.com/rudderlabs/rudder-auth/issues/385)) ([c693616](https://github.com/rudderlabs/rudder-auth/commit/c693616ac67d120bd3662e1f96b054d7ded98b0c))
* npm update glob, minimist, qs, normalize-url, http-cache-semantics, glob-parent high vulnerabilities(npm audit) ([43aaa2b](https://github.com/rudderlabs/rudder-auth/commit/43aaa2bd9976dc87a1ccd12d0c264d972c720b6f))
* npm vulnerabilities ([bf0536a](https://github.com/rudderlabs/rudder-auth/commit/bf0536aa8b5cc7d0347fde7f8d14e5c2b608dbf3))
* npm vulnerabilities ([c97e240](https://github.com/rudderlabs/rudder-auth/commit/c97e240be97c20da3dc506146e3c4072b7c54089))
* package.json & package-lock.json to reduce vulnerabilities ([65324eb](https://github.com/rudderlabs/rudder-auth/commit/65324ebfee36c8c40bed33fc53743dd17bc69c5f))
* proper image in release flow ([#262](https://github.com/rudderlabs/rudder-auth/issues/262)) ([3085df8](https://github.com/rudderlabs/rudder-auth/commit/3085df84f51ac4d77ee3dd4689b4c0fd51420713))
* reddit oauth implemenation redirect-uri fetching ([6a0d91c](https://github.com/rudderlabs/rudder-auth/commit/6a0d91c2632e658af6c7d9155be3aa8c3c624f0d))
* refresh token issue ([da27c8b](https://github.com/rudderlabs/rudder-auth/commit/da27c8b4501ec08fb0229982144715bcbff3836c))
* refresh token params changed to query parameters ([#288](https://github.com/rudderlabs/rudder-auth/issues/288)) ([2ab4eb8](https://github.com/rudderlabs/rudder-auth/commit/2ab4eb82908c01a8aa118e534dd260588d595d58))
* remove extra parameters from url path ([#381](https://github.com/rudderlabs/rudder-auth/issues/381)) ([6c77743](https://github.com/rudderlabs/rudder-auth/commit/6c77743e1adab9801254266586dedb1c61efb0ff))
* remove hubspot legacy scopes ([1b242d0](https://github.com/rudderlabs/rudder-auth/commit/1b242d0f1557d4699c20c4930942746fa3770162))
* remove shortid resulting in high vulnerability ([6e3fa30](https://github.com/rudderlabs/rudder-auth/commit/6e3fa304e8c28ff2f00dda7a5faf807376643780))
* remove unnecessary auth file for shopify ([0ff2d83](https://github.com/rudderlabs/rudder-auth/commit/0ff2d83188d3db10f6a85a9821e6336ee18b8807))
* remove unused jest, ts-jest and types/jest dependencies ([c6f10ec](https://github.com/rudderlabs/rudder-auth/commit/c6f10ec43b2c97d01b6cf392f5fc9541ecec5edc))
* removing shopify.ts and passport-shopify dependency ([ebd14a9](https://github.com/rudderlabs/rudder-auth/commit/ebd14a904289aec7578a4bf4490924146ed777d9))
* resolve dest name for client details ([2d13bef](https://github.com/rudderlabs/rudder-auth/commit/2d13bef87b9a66236bc47d4ca76f4f5285142963))
* return token case change ([f953490](https://github.com/rudderlabs/rudder-auth/commit/f95349048d3e482f2dadad3a7de2abc812b0f983))
* salesforce sandbox state ([#67](https://github.com/rudderlabs/rudder-auth/issues/67)) ([b0f7c3b](https://github.com/rudderlabs/rudder-auth/commit/b0f7c3b96c2147c4a732f47e2f4febf118729c1a))
* to handle contacts scope deprecation ([6937ce1](https://github.com/rudderlabs/rudder-auth/commit/6937ce14f08a6b6d8d4e522b4deed8134b3eed8f))
* update import in campaign_manager refresh method implementation ([#119](https://github.com/rudderlabs/rudder-auth/issues/119)) ([be0ae03](https://github.com/rudderlabs/rudder-auth/commit/be0ae03574ebc4b9f86f512875e9d34b48c67853))
* update refresh token method of bingads_audience ([#143](https://github.com/rudderlabs/rudder-auth/issues/143)) ([3606d27](https://github.com/rudderlabs/rudder-auth/commit/3606d2720df6385a7db2e10cc17443836af12e3b))
* update role name for singer google adwords ([#195](https://github.com/rudderlabs/rudder-auth/issues/195)) ([9d0d54f](https://github.com/rudderlabs/rudder-auth/commit/9d0d54f696c26cf20f8a58353d3c3289c83e2986))
* update scopes for hubspot source app ([#73](https://github.com/rudderlabs/rudder-auth/issues/73)) ([f1e65c5](https://github.com/rudderlabs/rudder-auth/commit/f1e65c5e21a22eff78efb0b9bc58c9d6d4bb0e2e))
* wrong axios resolved url & updated to a newer patch version ([9a5f1a0](https://github.com/rudderlabs/rudder-auth/commit/9a5f1a082caba7077c0bc1448bd6f07b0090c3b8))
* zendesk env variable ([#80](https://github.com/rudderlabs/rudder-auth/issues/80)) ([67a2825](https://github.com/rudderlabs/rudder-auth/commit/67a2825cdf9c0ea988c6e9ad2fd96d52884e3624))
* zoho domains for all ([#332](https://github.com/rudderlabs/rudder-auth/issues/332)) ([c11caed](https://github.com/rudderlabs/rudder-auth/commit/c11caed7eb11486b21ffb887e3b07d0d3bbcdd30))
* zoho invalid code issue ([#397](https://github.com/rudderlabs/rudder-auth/issues/397)) ([f33b804](https://github.com/rudderlabs/rudder-auth/commit/f33b804697bf866be98ce713c783f2aea98e9f36))


### Features

*  Different oauth creds for singer salesforce ([#64](https://github.com/rudderlabs/rudder-auth/issues/64)) ([a026353](https://github.com/rudderlabs/rudder-auth/commit/a026353b8745cfa4501ef672030599929da29a69))
* add new destination tiktok_audience ([#191](https://github.com/rudderlabs/rudder-auth/issues/191)) ([d89b61d](https://github.com/rudderlabs/rudder-auth/commit/d89b61d31a4b5f0d29b1f1f167af90211f072d39))
* add oauth for bingads audience destination ([b72a0ab](https://github.com/rudderlabs/rudder-auth/commit/b72a0ab14820141864c8268e729f6dcec7ff2625))
* add reddit destination oauth ([#214](https://github.com/rudderlabs/rudder-auth/issues/214)) ([f9a6bd5](https://github.com/rudderlabs/rudder-auth/commit/f9a6bd5d0bff848032654bd1e49759bbf6ff8aa7))
* add required scope to salesforce destination ([#277](https://github.com/rudderlabs/rudder-auth/issues/277)) ([fcec64e](https://github.com/rudderlabs/rudder-auth/commit/fcec64edb6e800e9a023302ec742301d3e7860bd))
* add salesforce destination oauth ([#198](https://github.com/rudderlabs/rudder-auth/issues/198)) ([18f2c9f](https://github.com/rudderlabs/rudder-auth/commit/18f2c9fa293c12bb257805c243964a3fc1dcb5e3)), closes [#11](https://github.com/rudderlabs/rudder-auth/issues/11) [#12](https://github.com/rudderlabs/rudder-auth/issues/12) [#13](https://github.com/rudderlabs/rudder-auth/issues/13) [#14](https://github.com/rudderlabs/rudder-auth/issues/14) [#15](https://github.com/rudderlabs/rudder-auth/issues/15) [#16](https://github.com/rudderlabs/rudder-auth/issues/16) [#18](https://github.com/rudderlabs/rudder-auth/issues/18) [#20](https://github.com/rudderlabs/rudder-auth/issues/20)
* add state to intercom oauth ([9fee5d0](https://github.com/rudderlabs/rudder-auth/commit/9fee5d0d197995493ebd2240eacbb8bdab3fc34d))
* add support for singer-zendesk-support connector ([#79](https://github.com/rudderlabs/rudder-auth/issues/79)) ([34ae823](https://github.com/rudderlabs/rudder-auth/commit/34ae823e859da45a7e01b94a28cc1255dc56b375))
* add tests and report coverage ([#240](https://github.com/rudderlabs/rudder-auth/issues/240)) ([647bbd7](https://github.com/rudderlabs/rudder-auth/commit/647bbd7cb8288ed22350b5c0f6c2f482631d1598))
* add unit tests for oAuthUtils.ts file ([#281](https://github.com/rudderlabs/rudder-auth/issues/281)) ([00f01be](https://github.com/rudderlabs/rudder-auth/commit/00f01bef26a9ed04fad2a520bfe1e2a825e3669e))
* add user profile option in twitter oauth ([#257](https://github.com/rudderlabs/rudder-auth/issues/257)) ([34eb2d1](https://github.com/rudderlabs/rudder-auth/commit/34eb2d1525291fb567e783b6ed451a47bac1492a))
* added oauth implementation of bing_ads ([f85beb3](https://github.com/rudderlabs/rudder-auth/commit/f85beb345c1cae27d8981d1f22b2789d4bcefae3))
* added return statement ([15de951](https://github.com/rudderlabs/rudder-auth/commit/15de9518ae078a7d207544a951c3e290656a1a0f))
* auth for campaign manager ([06cfdf6](https://github.com/rudderlabs/rudder-auth/commit/06cfdf6f06fd746f8d10ed6945a3df7178a32c55))
* **auth:** enables OAuth for Singer Google Search Console ([#86](https://github.com/rudderlabs/rudder-auth/issues/86)) ([e4dcd19](https://github.com/rudderlabs/rudder-auth/commit/e4dcd1908722b51fe07af0120d7a909e8fe8ed4a))
* **google-ads-offline-conversions:** bump version ([8995f9c](https://github.com/rudderlabs/rudder-auth/commit/8995f9cc8b3604c32b9d77bbbd3902479f33c171))
* **google-ads-offline-conversions:** revert version bump ([e838121](https://github.com/rudderlabs/rudder-auth/commit/e838121ae6fd1a560db7d2137eec775cadc4feb8))
* **google-ads-offline-conversions:** update destination name ([ebfb7cc](https://github.com/rudderlabs/rudder-auth/commit/ebfb7ccf7aa17cb5ab165754328ae544dec150fc))
* intercom oauth changes ([f1421ab](https://github.com/rudderlabs/rudder-auth/commit/f1421ab718a86ea8379523ffa1ebe52a1f3feb61))
* linkedin conversions ([#286](https://github.com/rudderlabs/rudder-auth/issues/286)) ([377c71d](https://github.com/rudderlabs/rudder-auth/commit/377c71d8d23c8154b38b8c9004f4fdf8bd37a15f))
* **new integration:** onboarding snapchat custom audience ([#88](https://github.com/rudderlabs/rudder-auth/issues/88)) ([60a19d1](https://github.com/rudderlabs/rudder-auth/commit/60a19d1aeb8cf153b17d7cc77f3e6743422d039f))
* **new integration:** updating snapchat custom audience ([#93](https://github.com/rudderlabs/rudder-auth/issues/93)) ([ce8277e](https://github.com/rudderlabs/rudder-auth/commit/ce8277e8452a421badd61d57e5ff4d454cd7017e))
* oauth access denied handling ([#172](https://github.com/rudderlabs/rudder-auth/issues/172)) ([bb1d562](https://github.com/rudderlabs/rudder-auth/commit/bb1d56289bbd3b137b6deb26a42498ef6f23df35))
* oauth config propagation ([#357](https://github.com/rudderlabs/rudder-auth/issues/357)) ([e97bc35](https://github.com/rudderlabs/rudder-auth/commit/e97bc35414650ccd6dd3fb6c90c2f6b74b78d0ca))
* onboard Amazon Audience ([#350](https://github.com/rudderlabs/rudder-auth/issues/350)) ([a05a43e](https://github.com/rudderlabs/rudder-auth/commit/a05a43ebac831368daad1bee4099cff9d0d21b1c))
* onboard Amazon Audience ([#363](https://github.com/rudderlabs/rudder-auth/issues/363)) ([7e71091](https://github.com/rudderlabs/rudder-auth/commit/7e710916dfb49367576064b89d2bd9349fdf85a5))
* onboard linkedin audience destination ([#380](https://github.com/rudderlabs/rudder-auth/issues/380)) ([fd3d4ea](https://github.com/rudderlabs/rudder-auth/commit/fd3d4ea1a946ca6e269e7a25db9a02c63b22ddcf))
* onboard new destination bing_ads_offline_conversion ([5816171](https://github.com/rudderlabs/rudder-auth/commit/5816171f77aa84f746f0fe0fd59746ef55c3dafd))
* onboard oauth for ga4 ([#293](https://github.com/rudderlabs/rudder-auth/issues/293)) ([a670269](https://github.com/rudderlabs/rudder-auth/commit/a670269d96f2e33ec5588f8be66c2b970364e4cb))
* onboard X(Twiiter) Audience ([#344](https://github.com/rudderlabs/rudder-auth/issues/344)) ([6fb4006](https://github.com/rudderlabs/rudder-auth/commit/6fb40064d1507ef308c289b33feeef55e11807ff))
* onboard yandex metrica offline events ([#278](https://github.com/rudderlabs/rudder-auth/issues/278)) ([ea0a3c6](https://github.com/rudderlabs/rudder-auth/commit/ea0a3c6af34c44de7b2bb0c0ee7215a3c5c2ceee))
* onboarding criteo audience ([a91cbe6](https://github.com/rudderlabs/rudder-auth/commit/a91cbe6fa214b1035f1cebb612bf13dd966b93fd))
* onboarding criteo audience ([affe4de](https://github.com/rudderlabs/rudder-auth/commit/affe4de28c3b29994e7ba6ec3fcc7c0210dcab6b))
* onboarding intercom destination oauth support ([#348](https://github.com/rudderlabs/rudder-auth/issues/348)) ([d1d3ad2](https://github.com/rudderlabs/rudder-auth/commit/d1d3ad23dfdf07b7c49b2d5a5d955fd56b5a8d4d))
* onboarding oauth for criteo ([d018942](https://github.com/rudderlabs/rudder-auth/commit/d01894221fb4c0fd057d23b32e1dcf136848df3d))
* optional scope provision for hubspot ([794a7ba](https://github.com/rudderlabs/rudder-auth/commit/794a7ba96a874e13e1964a5ef62aa4739ff0b152))
* refactored bing_ads ([828b468](https://github.com/rudderlabs/rudder-auth/commit/828b4685b90890e1785bfa53cb1d8d10a677a3d1))
* singer-zendesk-chat ([#82](https://github.com/rudderlabs/rudder-auth/issues/82)) ([42d8f99](https://github.com/rudderlabs/rudder-auth/commit/42d8f99f396dd19b733d1d4b9338242782333397))
* **snapchat_custom_audience:** master branch PR ([#106](https://github.com/rudderlabs/rudder-auth/issues/106)) ([cf3d4fd](https://github.com/rudderlabs/rudder-auth/commit/cf3d4fd81be3ca06090e8947ddfcfafb9fcbaf03))
* **snapchat_custom_audience:** updating scope ([#105](https://github.com/rudderlabs/rudder-auth/issues/105)) ([f2e3581](https://github.com/rudderlabs/rudder-auth/commit/f2e3581ef3b89bbc2cdd94bf8d915aedbf460c2d))
* support for singer-facebook-marketing ([#97](https://github.com/rudderlabs/rudder-auth/issues/97)) ([85cedec](https://github.com/rudderlabs/rudder-auth/commit/85cedecc4e92b72fb7dcffe1feb3bafffd9e9f86))
* support pipedrive ([#133](https://github.com/rudderlabs/rudder-auth/issues/133)) ([ae970e8](https://github.com/rudderlabs/rudder-auth/commit/ae970e822ace385009224154cf9ba3fa0e961a0e))
* twitter ads extended to support destination ([7b4b843](https://github.com/rudderlabs/rudder-auth/commit/7b4b843cbb691b940076be549a69e7d90e2f7ef7))
* upgrade node version ([#178](https://github.com/rudderlabs/rudder-auth/issues/178)) ([202030f](https://github.com/rudderlabs/rudder-auth/commit/202030fa2eea80f4eeb8be563f6b92adba170ee0)), closes [#179](https://github.com/rudderlabs/rudder-auth/issues/179)
* zoho oauth ([#327](https://github.com/rudderlabs/rudder-auth/issues/327)) ([9553086](https://github.com/rudderlabs/rudder-auth/commit/955308611bf04db68ae131814a556fe53da0cefb))


### Reverts

* Revert "feat: onboard Amazon Audience ([#350](https://github.com/rudderlabs/rudder-auth/issues/350))" ([#359](https://github.com/rudderlabs/rudder-auth/issues/359)) ([f2c4945](https://github.com/rudderlabs/rudder-auth/commit/f2c4945ebe197c6e6998b4f10bda1215e1dc9f82))
