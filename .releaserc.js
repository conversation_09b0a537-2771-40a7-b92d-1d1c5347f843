module.exports = {
  branches: ['master', 'develop', 'release/*', 'hotfix-release/*'],
  plugins: [
    [
      '@semantic-release/commit-analyzer',
      {
        preset: 'conventionalcommits',
        parserOpts: {
          noteKeywords: ['BREAKING CHANGE', 'BREAKING CHANGES', 'BREAKING'],
        },
      },
    ],
    [
      '@semantic-release/release-notes-generator',
      {
        preset: 'conventionalcommits',
        presetConfig: {
          types: [
            { type: 'feat', section: '✨ Features', hidden: false },
            { type: 'fix', section: '🐛 Bug Fixes', hidden: false },
            { type: 'chore', section: '🔧 Chores', hidden: false },
            { type: 'docs', section: '📚 Documentation', hidden: false },
            { type: 'style', section: '💎 Styles', hidden: false },
            { type: 'refactor', section: '♻️ Code Refactoring', hidden: false },
            { type: 'perf', section: '🚀 Performance Improvements', hidden: false },
            { type: 'test', section: '🔍 Tests', hidden: false },
            { type: 'merge', section: '🔄 Merges', hidden: false },
          ],
        },
      },
    ],
    '@semantic-release/changelog',
    [
      '@semantic-release/exec',
      {
        prepareCmd: 'echo "${nextRelease.notes}" > RELEASE_NOTES.md',
      },
    ],
  ],
  preset: 'conventionalcommits',
  releaseRules: [
    { type: 'chore', release: 'patch' },
    { type: 'refactor', release: 'patch' },
  ],
};
