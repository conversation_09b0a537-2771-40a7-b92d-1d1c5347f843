import express from 'express';
import morgan from 'morgan';
import bodyParser from 'body-parser';
import helmet from 'helmet';
import url from 'url';
import swaggerUi from 'swagger-ui-express';
import YAML from 'yamljs';
import path from 'path';
import errorHandler from './lib/errorHandler';
import routes from './routes';
import { metricsRouteHandler, metrics } from './lib/metrics';

const app: express.Application = express();

// Load Swagger document
const swaggerPath = path.join(__dirname, '../swagger.yaml');

const swaggerDocument = YAML.load(swaggerPath);

// Swagger UI setup
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));

app.use(async (req, res, next) => {
  const start = Date.now();
  await next();
  const { pathname } = url.parse(req.url);
  const duration = Date.now() - start;
  metrics.httpRequestDuration.observe(
    {
      method: req.method,
      path: pathname ?? req.url,
      status: res.statusCode,
    },
    duration / 1000,
  );
});
app.use(bodyParser.json(), bodyParser.urlencoded({ extended: true }));
app.use('/health', (req, res) => {
  res.status(204).end();
});
app.get('/metrics', metricsRouteHandler);

app.use(morgan('combined'));
app.use(
  helmet({
    // Allow Swagger UI to work properly
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      },
    },
  }),
);
app.use(routes);
app.use(errorHandler);
export default app;
