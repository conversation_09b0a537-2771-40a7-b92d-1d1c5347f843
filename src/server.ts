import dotenv from 'dotenv';
import config from 'config';
import app from './app';

dotenv.config();

process.on('uncaughtException', (err) => {
  console.error(err);
  process.exit(1);
});

process.on('unhandledRejection', (err) => {
  console.error(err);
  process.exit(1);
});

const server = app.listen(config.get('port'), config.get('host'), () => {
  console.log(`rudder-auth started on ${config.get('host')}:${config.get('port')}`);
});

server.keepAliveTimeout = 61 * 1000;
server.headersTimeout = 65 * 1000;
