export interface OAuthImplementation {
  getAuthMiddleware(): any;
  getCallbackMiddleware(): any;
  refreshToken(refreshToken: string, options: Record<string, any>): Promise<any>;
}

export interface AccountMetadata {
  userId?: string;
  email?: string;
  displayName?: string;
}

export type AccountDefinition = {
  name: string;
  type: string;
  category: string;
  authenticationType: string;
};

export interface AuthPayload {
  account: {
    options: Record<string, any>;
    secret?: Record<string, any>;
    accountDefinitionName: string;
  };
  accountDefinition: AccountDefinition;
}
