import axios from 'axios';
import qs from 'querystring';
import { clientCredsConfig } from 'client-configs';
import oAuthUtils from '../../lib/utils/oAuthUtils';
import { RudderCategory, roles } from '../../lib/constants';

export default async (credentials: any, role: string, rudderCategory?: RudderCategory) => {
  const envKey = oAuthUtils.getEnvironmentVariableKey(role, rudderCategory);
  const { client_id, client_secret } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);
  const { instance_url, refresh_token } = credentials;

  try {
    const response = await axios.post(
      `${instance_url}/services/oauth2/token`,
      qs.stringify({
        grant_type: 'refresh_token',
        refresh_token,
        client_id,
        client_secret,
      }),
    );

    return response.data;
  } catch (err) {
    // This condition refers to a failure of Refresh Token
    // https://help.salesforce.com/s/articleView?id=sf.remoteaccess_revoke_token.htm&type=5
    // if we use the /revoke endpoint for revoking refreshToken
    // hence refreshToken will raise an "invalid_grant" error
    return oAuthUtils.handleInvalidGrantError(roles.SALESFORCE, err);
  }
};
