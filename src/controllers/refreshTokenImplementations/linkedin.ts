import axios from 'axios';
import moment from 'moment';
import { clientCredsConfig } from 'client-configs';
import { RudderCategory } from '../../lib/constants';
import oAuthUtils from '../../lib/utils/oAuthUtils';

export default async (credentials: any, role: string, rudderCategory?: RudderCategory) => {
  const envKey = oAuthUtils.getEnvironmentVariableKey(role, rudderCategory);
  const { client_id, client_secret } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);

  const { refreshToken } = credentials;

  try {
    const response = await axios.post('https://www.linkedin.com/oauth/v2/accessToken', null, {
      params: {
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id,
        client_secret,
      },
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    if (response.status === 200) {
      const { access_token, refresh_token, expires_in } = response.data;
      return {
        accessToken: access_token,
        refreshToken: refresh_token,
        expirationDate: moment().add(expires_in, 's').toISOString(),
      };
    }
    return response.data;
  } catch (err) {
    return oAuthUtils.handleInvalidGrantError(role, err);
  }
};
