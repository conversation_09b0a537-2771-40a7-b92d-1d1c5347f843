import axios from 'axios';
import qs from 'querystring';
import { clientCredsConfig } from 'client-configs';
import { RudderCategory } from '../../lib/constants';
import oAuthUtils from '../../lib/utils/oAuthUtils';

export default async (credentials: any, role: string, rudderCategory?: RudderCategory) => {
  const envKey = oAuthUtils.getEnvironmentVariableKey(role, rudderCategory);
  const { client_id, client_secret } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);

  const { refresh_token } = credentials;

  try {
    const response = await axios.post(
      'https://www.googleapis.com/oauth2/v3/token',
      qs.stringify({
        grant_type: 'refresh_token',
        refresh_token,
        client_id,
        client_secret,
      }),
    );
    if (response.status === 200) {
      const { access_token, refresh_token: refreshToken } = response.data;
      return { access_token, refresh_token: refreshToken };
    }
    return response.data;
  } catch (err) {
    return oAuthUtils.handleInvalidGrantError(role, err);
  }
};
