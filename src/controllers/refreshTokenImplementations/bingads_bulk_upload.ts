import axios from 'axios';
import qs from 'querystring';
import moment from 'moment';
import { googleConfig } from 'client-configs';
import { RudderCategory } from '../../lib/constants';
import oAuthUtils from '../../lib/utils/oAuthUtils';

export default async (credentials: any, role: string, rudderCategory?: RudderCategory) => {
  const envKey = oAuthUtils.getEnvironmentVariableKey(role, rudderCategory);
  const { client_id, client_secret } = oAuthUtils.getFromConfig<googleConfig>(envKey);

  const { refreshToken } = credentials;
  const grant_type = 'refresh_token';
  const data = qs.stringify({
    grant_type,
    refresh_token: refreshToken,
    client_id,
    client_secret,
    scope: 'openid offline_access https://ads.microsoft.com/msads.manage',
  });
  try {
    const response = await axios.post(
      'https://login.microsoftonline.com/common/oauth2/v2.0/token',
      data,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
    if (response.status === 200) {
      const { access_token, refresh_token, expires_in } = response.data;
      return {
        accessToken: access_token,
        refreshToken: refresh_token,
        expirationDate: moment().add(expires_in, 's').toISOString(),
      };
    }
    return response.data;
  } catch (err) {
    // API Doc: https://learn.microsoft.com/en-us/advertising/guides/handle-service-errors-exceptions?view=bingads-13#common-oauth-errors
    return oAuthUtils.handleInvalidGrantError(role, err);
  }
};
