import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';
// register passport strategy
const BingAdsStrategy = require('passport-bing-ads').Strategy;

export default (role: string) => {
  const router: express.Router = express.Router();
  const oauthOptions = {
    ...utils.oauthStrategyOptions(role),
    scope: 'https://ads.microsoft.com/msads.manage offline_access openid',
    authorizationURL: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
    tokenURL: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
  };

  passport.use(
    role,
    new BingAdsStrategy(
      oauthOptions,
      (accessToken: string, refreshToken: string, profile: any, callback: TSPassportCallback) => {
        utils.handleCallback(callback, {
          role,
          profile,
          secret: { accessToken, refreshToken },
        });
      },
    ),
  );

  // setup authentication and callback routes
  router.get('/authz', passport.authenticate(role));

  router.get('/callback', passport.authorize(role), (req: any, res) => {
    res.send(req.account);
  });
  return router;
};
