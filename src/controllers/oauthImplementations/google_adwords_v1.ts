import express from 'express';
import passport from 'passport';
import { AuthSetupOptions, TSPassportCallback } from 'auth';
import { developerTokenConfig } from 'client-configs';
import utils from '../../lib/utils/oAuthUtils';
import logger from '../../logger';
import { RudderCategory } from '../../lib/constants';

export default (authSetupOpts: AuthSetupOptions) => {
  const { authenticateOptions, role } = authSetupOpts;
  const router: express.Router = express.Router();
  const { developer_token } = utils.getFromConfig<developerTokenConfig>(
    `oauth.credentials.${role}`,
  );
  // register passport strategy
  const GoogleStrategy = require('passport-google-oauth20').Strategy;
  Object.values(RudderCategory).forEach((rudderCategory) => {
    const opts = utils.oauthStrategyOptions(role, rudderCategory);
    logger.debug(`[${role}] opts: ${JSON.stringify(opts)}`);
    const strategyName = utils.getStrategyName(role, rudderCategory);
    passport.use(
      strategyName,
      new GoogleStrategy(
        opts,
        (
          access_token: string,
          refresh_token: string,
          profile: any,
          callback: TSPassportCallback,
        ) => {
          utils.handleCallback(callback, {
            role,
            profile,
            secret: { access_token, refresh_token, developer_token },
          });
        },
      ),
    );
  });

  const authzHandler = (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction,
  ) => {
    const rudderCategory = req.rudderCategory as RudderCategory;
    const strategyName = utils.getStrategyName(role, req.rudderCategory);
    const authOptions = authenticateOptions?.get(rudderCategory);
    logger.debug(
      `[${role}] authzHandler strategyName: ${strategyName} authenticationOptions: ${authOptions}`,
    );
    if (authOptions) {
      return passport.authenticate(strategyName, authOptions)(req, res, next);
    }
    return passport.authenticate(strategyName)(req, res, next);
  };

  const callbackHandler = (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction,
  ) => {
    const strategyName = utils.getStrategyName(role, req.rudderCategory);
    return passport.authorize(strategyName)(req, res, next);
  };

  // setup authentication and callback routes
  router.get('/authz', authzHandler);

  router.get('/callback', callbackHandler, (req: any, res) => {
    res.send(req.account);
  });

  return router;
};
