import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';
import { roles } from '../../lib/constants';

export default (role: string, optional_scope: string) => {
  const router: express.Router = express.Router();

  const scope = ['crm.schemas.contacts.read', 'crm.objects.contacts.read'];

  // register passport strategy
  const HubspotStrategy = require('../../lib/passport/hubspot').Strategy;
  passport.use(
    role,
    new HubspotStrategy(
      {
        ...utils.oauthStrategyOptions(role),
        callbackURL: utils.callbackURLByRole(roles.HUBSPOT),
      },
      (accessToken: string, refreshToken: string, profile: any, callback: TSPassportCallback) => {
        utils.handleCallback(callback, {
          role: roles.HUBSPOT,
          profile,
          secret: { accessToken, refreshToken },
        });
      },
    ),
  );

  // setup authentication and callback routes
  router.get(
    '/authz',
    // @ts-expect-error: overload with "optional_scope" is not available
    passport.authenticate(role, {
      scope,
      optional_scope,
    }),
  );

  router.get('/callback', passport.authorize(role), (req: any, res) => {
    res.send(req.account);
  });
  return router;
};
