import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import moment from 'moment';
import utils from '../../lib/utils/oAuthUtils';

const LinkedInStrategy = require('passport-linkedin-oauth2').Strategy;

export default (role: string, scope: string[], profileFields: string[], rudderCategory: string) => {
  const router: express.Router = express.Router();
  const strategyName = utils.getStrategyName(role, rudderCategory);
  const oauthOptions = {
    ...utils.oauthStrategyOptions(role, rudderCategory),
    scope,
    profileFields,
  };

  passport.use(
    strategyName,
    new LinkedInStrategy(
      oauthOptions,
      (
        accessToken: string,
        refreshToken: string,
        response: any,
        profile: any,
        callback: TSPassportCallback,
      ) => {
        utils.handleCallback(callback, {
          role,
          profile,
          secret: {
            accessToken,
            refreshToken,
            expirationDate: moment().add(response.expires_in, 's').toISOString(),
          },
        });
      },
    ),
  );

  // setup authentication and callback routes
  router.get('/authz', (req: express.Request, res: express.Response, next: express.NextFunction) =>
    passport.authenticate(strategyName)(req, res, next),
  );

  router.get(
    '/callback',
    (req: express.Request, res: express.Response, next: express.NextFunction) =>
      passport.authorize(strategyName)(req, res, next),
    (req: any, res) => {
      res.send(req.account);
    },
  );
  return router;
};
