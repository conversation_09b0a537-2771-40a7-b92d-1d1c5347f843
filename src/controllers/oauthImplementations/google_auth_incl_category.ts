import express from 'express';
import passport from 'passport';
import { AuthSetupOptions, TSPassportCallback } from 'auth';
import PassportGoogleOAuth2 from 'passport-google-oauth20';
import utils from '../../lib/utils/oAuthUtils';
import logger from '../../logger';
import { RudderCategory } from '../../lib/constants';

// TODO: Probably this needs to be moved to some kind of utils
/**
 * Provide the express.Router instance for making sure that OAuth requests are served
 * @param authSetupOptions
 * @returns
 */
export const getOAuthRouter = (authSetupOptions: AuthSetupOptions) => {
  const router: express.Router = express.Router();
  const { role, authenticateOptions } = authSetupOptions;

  const authzHandler = (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction,
  ) => {
    const rudderCategory = req.rudderCategory as RudderCategory;
    const strategyName = utils.getStrategyName(role, req.rudderCategory);
    logger.info(`StrategyName: ${strategyName}`);
    const authOptions = authenticateOptions?.get(rudderCategory || RudderCategory.Source);
    logger.debug(
      `[${role}] authzHandler strategyName: ${strategyName} authenticationOptions: ${authOptions}`,
    );
    if (authOptions) {
      return passport.authenticate(strategyName, authOptions)(req, res, next);
    }
    return passport.authenticate(strategyName)(req, res, next);
  };

  const callbackHandler = (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction,
  ) => {
    const strategyName = utils.getStrategyName(role, req.rudderCategory);
    return passport.authorize(strategyName)(req, res, next);
  };

  // setup authentication and callback routes
  router.get('/authz', authzHandler);

  router.get('/callback', callbackHandler, (req: any, res) => {
    res.send(req.account);
  });

  return router;
};

/**
 * This Google OAuth implementation is to be used on destination/sources which needs very similar kind of OAuth
 * for all types of RudderCategory
 *
 * Anything in very specific to the destination has to be implemented in it's own way
 */
export default (authSetupOptions: AuthSetupOptions) => {
  const { role, supportLegacy } = authSetupOptions;

  const GoogleStrategy = PassportGoogleOAuth2.Strategy;

  let categories: Record<string, string | undefined> = RudderCategory;
  if (supportLegacy) {
    // Legacy calls(without rudderCategory in url) needs to be supported for backward compatibility
    // none: undefined -> category indicates the legacy calls(ex: "/google_analytics/authz")
    categories = { ...categories, none: undefined };
  }
  Object.values(categories).forEach((rudderCategory) => {
    const opts = utils.oauthStrategyOptions(role, rudderCategory);
    logger.debug(`[${role}] opts: ${JSON.stringify(opts)}`);
    const strategyName = utils.getStrategyName(role, rudderCategory);
    passport.use(
      strategyName,
      new GoogleStrategy(
        opts,
        (
          access_token: string,
          refresh_token: string,
          profile: any,
          callback: TSPassportCallback,
        ) => {
          utils.handleCallback(callback, {
            role,
            profile,
            secret: { access_token, refresh_token },
          });
        },
      ),
    );
  });

  return getOAuthRouter(authSetupOptions);
};
