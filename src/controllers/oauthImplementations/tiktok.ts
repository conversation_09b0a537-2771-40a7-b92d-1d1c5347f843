import express from 'express';
import passport from 'passport';
import { AuthSetupOptions, TSPassportCallback } from 'auth';
import passportOauth2 from 'passport-oauth2';
import url from 'url';
import axios from 'axios';
import { parse } from 'lossless-json';
import { RudderCategory } from '../../lib/constants';
import logger from '../../logger';
import utils from '../../lib/utils/oAuthUtils';

class TikTokAudience extends passportOauth2.Strategy {
  constructor(
    options: passportOauth2.StrategyOptions,
    verifyFunction: passportOauth2.VerifyFunction,
  ) {
    const { ...restOptions } = options;
    super(restOptions, verifyFunction);
  }
}
let role: string;
let opts: any;

const authzHandler = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  return passport.authenticate(strategyName)(req, res, next);
};

const callbackHandler = async (req: express.Request, res: express.Response) => {
  const urlObject = url.parse(req.url, true);
  const data = {
    grant_type: 'authorization_code',
    client_id: opts.clientID,
    client_secret: opts.clientSecret,
    code: urlObject.query.code,
  };
  try {
    const response = await axios.request({
      method: 'POST',
      url: 'https://business-api.tiktok.com/open_api/v1.3/oauth/token/',
      headers: {
        'Content-Type': 'application/json',
      },
      data,
      responseType: 'arraybuffer',
    });
    const responseData = parse(String(response.data));
    res.send({
      secret: {
        accessToken: Object(responseData)?.access_token,
        advertiserIds: [Object(responseData)?.advertiser_ids[0]?.value],
      },
    });
  } catch (err) {
    res.status(400).send({ success: false, err });
  }
};

const strategyCreator = (rudderCategory: string | undefined) => {
  opts = utils.oauthStrategyOptions(role, rudderCategory);
  logger.debug(`[${role}] opts: ${JSON.stringify(opts)}`);
  const strategyName = utils.getStrategyName(role, rudderCategory);
  passport.use(
    strategyName,
    new TikTokAudience(
      {
        ...opts,
        authorizationURL: `https://business-api.tiktok.com/portal/auth?app_id=${opts.clientID}`,
        tokenURL: 'https://business-api.tiktok.com/open_api/v1.3/oauth/token/',
      },
      (
        access_token: string,
        refresh_token: string,
        results: any,
        profile: any,
        callback: TSPassportCallback,
      ) => {
        utils.handleCallback(callback, {
          role,
          profile,
          secret: {
            accessToken: access_token,
            advertiserIds: results.advertiser_ids,
          },
        });
      },
    ),
  );
};

export default (authSetupOpts: AuthSetupOptions) => {
  role = authSetupOpts.role;
  const router: express.Router = express.Router();
  Object.values(RudderCategory).forEach(strategyCreator);
  // setup authentication and callback routes
  router.get('/authz', authzHandler);
  router.get('/callback', callbackHandler, (req: any, res) => {
    res.send(req.account);
  });
  return router;
};
