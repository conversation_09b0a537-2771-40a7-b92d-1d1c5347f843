import express from 'express';
import passport from 'passport';
import randomString from 'random-string';
import { RudderCategory } from '../../lib/constants';
import { TSPassportCallback } from '../../../types/auth';
import utils from '../../lib/utils/oAuthUtils';

export default (role: string) => {
  const router: express.Router = express.Router();

  // register passport strategy
  const IntercomStrategy = require('passport-intercom').Strategy;
  Object.values(RudderCategory).forEach((rudderCategory) => {
    const strategyName = utils.getStrategyName(role, rudderCategory);
    passport.use(
      strategyName,
      new IntercomStrategy(
        {
          ...utils.oauthStrategyOptions(role, rudderCategory),
          requireVerifiedEmail: false,
        },
        (accessToken: string, refreshToken: string, profile: any, callback: TSPassportCallback) => {
          utils.handleCallback(callback, {
            role,
            profile,
            secret: { accessToken, refreshToken },
          });
        },
      ),
    );
  });

  // setup authentication and callback routes
  router.get('/authz', (req, rest, next) => {
    // Pass state to the callback to make sure that the callback is not called by a malicious user
    const strategyName = utils.getStrategyName(role, req.rudderCategory);
    passport.authenticate(strategyName, { state: randomString() })(req, rest, next);
  });

  router.get(
    '/callback',
    (req, res, next) => {
      const { state } = req.query;
      if (state === undefined || state === '') {
        res.status(400).send('State is missing.');
        return;
      }
      /*
            The state is a random string that is generated in the authz route and passed to the callback.
            Ideally we should save the state and compare it with the one we get in the callback.
            Since node is single threaded we can't save the state in the memory and compare it with the one we get in the callback.
            So skipping the state comparison for now.
        */
      const strategyName = utils.getStrategyName(role, req.rudderCategory);
      passport.authorize(strategyName)(req, res, next);
    },
    (req: any, res) => {
      res.send(req.account);
    },
  );
  return router;
};
