import express from 'express';
import passport from 'passport';
// @ts-expect-error: dependency installed but for some reason error is not going away
import FB from 'fb';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';

const defaultScope = ['ads_read'];

export default (role: string, scope: string[] = defaultScope) => {
  const oAuthOptions = utils.oauthStrategyOptions(role);
  const FacebookAdsStrategy = require('passport-facebook').Strategy;
  passport.use(
    role,
    new FacebookAdsStrategy(
      oAuthOptions,
      (accessToken: string, refreshToken: string, profile: any, callback: TSPassportCallback) => {
        FB.api(
          'oauth/access_token',
          {
            client_id: oAuthOptions.clientID,
            client_secret: oAuthOptions.clientSecret,
            grant_type: 'fb_exchange_token',
            fb_exchange_token: accessToken,
            profileFields: ['id', 'emails', 'name'],
          },
          (response: any) => {
            const error = !response || response.error;
            if (error) {
              callback(error, null);
            } else {
              const longLivedAccessToken = response.access_token;
              utils.handleCallback(callback, {
                role,
                profile,
                secret: {
                  access_token: longLivedAccessToken,
                  refresh_token: refreshToken,
                },
              });
            }
          },
        );
      },
    ),
  );
  const router: express.Router = express.Router();

  // setup authentication and callback routes
  router.get('/authz', passport.authenticate(role, { scope }));

  router.get('/callback', passport.authorize(role, { scope }), (req: any, res) => {
    res.send(req.account);
  });
  return router;
};
