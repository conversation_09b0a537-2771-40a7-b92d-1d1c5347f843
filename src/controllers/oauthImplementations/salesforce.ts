import express from 'express';
import passport from 'passport';
import { SalesforceRoles, TSPassportCallback } from 'auth';
import { URL } from 'url';
import utils, { accountInfo, callbackUrlByRoleAndCategory } from '../../lib/utils/oAuthUtils';
import { RudderCategory } from '../../lib/constants';

const SalesforceStrategy = require('passport-salesforce').Strategy;

/*
    Registers the passport strategy using the provided role
    Return a router with /authz and /callback paths 
*/
interface SalesforceOptions {
  role: SalesforceRoles;
  isSandbox?: boolean;
  accountRole?: string;
  accountNameSuffix?: string;
  sandboxCallbackURLByRole?: string;
  oauthStrategyOptionsRole?: string;
}
function registerSalesforceStrategy(options: SalesforceOptions, withRouter: true): express.Router;

/*
    Registers the passport strategy using the provided role
    It doesn't return a router
*/
function registerSalesforceStrategy(options: SalesforceOptions, withRouter: false): void;
// eslint-disable-next-line consistent-return -- Adding this as this part of source-code is written with our use-cases in mind & has been working well(all this while)
function registerSalesforceStrategy(
  {
    role,
    isSandbox,
    accountRole,
    accountNameSuffix,
    sandboxCallbackURLByRole,
    oauthStrategyOptionsRole,
  }: SalesforceOptions,
  withRouter: boolean = true,
): express.Router | void {
  // register passport strategy
  // const SalesforceStrategy = require("passport-salesforce").Strategy;

  Object.values(RudderCategory).forEach((rudderCategory) => {
    let strategyOptions: Record<string, any> = utils.oauthStrategyOptions(
      oauthStrategyOptionsRole || role,
      rudderCategory,
    );
    if (rudderCategory === RudderCategory.Destination && role === 'salesforce') {
      strategyOptions.scope = 'offline_access api';
      strategyOptions.authorizationURL =
        'https://login.salesforce.com/services/oauth2/authorize?prompt=select_account';
    }
    if (isSandbox) {
      strategyOptions = {
        ...strategyOptions,
        callbackURL:
          rudderCategory === 'destination'
            ? callbackUrlByRoleAndCategory(role, rudderCategory as RudderCategory)
            : utils.callbackURLByRole(sandboxCallbackURLByRole || role),
        authorizationURL: 'https://test.salesforce.com/services/oauth2/authorize',
        tokenURL: 'https://test.salesforce.com/services/oauth2/token',
        profileURL: 'https://test.salesforce.com/services/oauth2/userinfo',
      };
    }
    const strategyName = utils.getStrategyName(role, rudderCategory);
    passport.use(
      strategyName,
      new SalesforceStrategy(
        strategyOptions,
        (
          access_token: string,
          refresh_token: string,
          profile: any,
          callback: TSPassportCallback,
        ) => {
          const { hostname } = new URL(profile.urls.rest);
          const instance_url = `https://${hostname}`;
          utils.handleCallback(callback, {
            renderAccountInfo(actProfile) {
              const accountData = accountInfo(actProfile);
              if (accountNameSuffix)
                accountData.accountName = `${accountData.accountName}${accountNameSuffix}`;
              return accountData;
            },
            role: accountRole || role,
            profile,
            options: {
              instance_url,
            },
            secret: { access_token, refresh_token, instance_url },
          });
        },
      ),
    );
  });

  if (withRouter) {
    const router: express.Router = express.Router();
    // setup authentication and callback routes
    router.get(
      '/authz',
      (req: express.Request, res: express.Response, next: express.NextFunction) => {
        const strategyName = utils.getStrategyName(role, req.rudderCategory);
        return passport.authenticate(strategyName)(req, res, next);
      },
    );

    router.get(
      '/callback',
      (req: express.Request, res: express.Response, next: express.NextFunction) => {
        const strategyName = utils.getStrategyName(role, req.rudderCategory);
        return passport.authorize(strategyName)(req, res, next);
      },
      (req: any, res) => {
        res.send(req.account);
      },
    );
    return router;
  }
}

export default registerSalesforceStrategy;
