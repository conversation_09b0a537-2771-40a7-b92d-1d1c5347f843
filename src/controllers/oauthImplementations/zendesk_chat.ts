import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';
import logger from '../../logger';

const notStateErrorMessage = 'State not found';
const emptySubdomainPostfix = '-empty-subdomain';
export default (role: string) => {
  const router: express.Router = express.Router();
  const scope = ['read'];
  const ZendeskChatStrategy = require('../../lib/passport/zendesk_chat').Strategy;
  passport.use(
    role,
    new ZendeskChatStrategy(
      {
        ...utils.oauthStrategyOptions(role),
        passReqToCallback: true,
      },
      (
        req: any,
        accessToken: string,
        refreshToken: string,
        profile: any,
        callback: TSPassportCallback,
      ) => {
        const subdomain = (req.session && req.session.subdomain) || req.query.subdomain;
        const options = { subdomain };
        let accountName = 'zendesk.com';
        if (subdomain) {
          accountName = `${subdomain}.${accountName}`;
        }
        utils.handleCallback(callback, {
          renderAccountInfo: () => ({
            accountName,
            metadata: { userId: accountName },
          }),
          role,
          profile: { ...profile, subdomain },
          options,
          secret: { accessToken, refreshToken },
        });
      },
    ),
  );

  function zendeskChatStrategy(subdomain?: string): string {
    if (!subdomain) {
      return `${role}${emptySubdomainPostfix}`;
    }
    const slug =
      (typeof subdomain === 'string' && subdomain.split('.')[0]) || emptySubdomainPostfix;
    if (slug === emptySubdomainPostfix) {
      logger.info(
        'slug is being evaluated as ',
        emptySubdomainPostfix,
        ' for subdomain',
        subdomain,
      );
    }
    return `${role}-${slug}`;
  }

  router.get('/authz', (req: any, res, next) => {
    const { subdomain } = req.query;
    const strategy = zendeskChatStrategy(subdomain);
    if (req.session && subdomain) {
      req.session.subdomain = subdomain;
    }

    passport.use(
      strategy,
      new ZendeskChatStrategy(
        {
          ...utils.oauthStrategyOptions(role),
          scope,
        },
        // this verify callback is not used, as callback is received by another Zendesk strategy 'zendesk_chat', common for all subdomains
        () => {},
      ),
    );

    const state = subdomain
      ? Buffer.from(JSON.stringify({ subdomain })).toString('base64')
      : undefined;
    const options = {
      state,
      subdomain: subdomain || '',
    };
    return passport.authenticate(strategy, options)(req, res, next);
  });

  router.get(
    '/callback',
    (req, res, next) => {
      const { state } = req.query;
      if (!state) {
        next(notStateErrorMessage);
      }
      const parsedState = JSON.parse(Buffer.from(state as string, 'base64').toString());
      req.query.subdomain = parsedState.subdomain;
      passport.authorize(role)(req, res, next);
    },
    (req: any, res) => {
      res.send(req.account);
    },
  );
  return router;
};
