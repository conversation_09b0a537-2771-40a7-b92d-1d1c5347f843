import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';

const subdomainErrorMessage = 'Subdomain is missing';
export default (role: string) => {
  const router: express.Router = express.Router();
  const ZendeskStrategy = require('passport-zendesk').Strategy;

  passport.use(
    role,
    new ZendeskStrategy(
      {
        ...utils.oauthStrategyOptions(role),
        passReqToCallback: true,
        scope: 'read',
      },
      (
        req: any,
        accessToken: string,
        refreshToken: string,
        profile: any,
        callback: TSPassportCallback,
      ) => {
        const { subdomain } = req.query;
        utils.handleCallback(callback, {
          renderAccountInfo: (actProfile) => ({
            accountName: `${actProfile.displayName} (${subdomain}.zendesk.com)`,
            metadata: { userId: actProfile.id },
          }),
          role,
          profile: { ...profile, subdomain },
          options: {
            subdomain,
          },
          secret: { accessToken, refreshToken },
        });
      },
    ),
  );

  router.get('/authz', (req, rest, next) => {
    const { subdomain } = req.query;
    if (!subdomain) {
      next(subdomainErrorMessage);
      return;
    }
    const state = Buffer.from(JSON.stringify({ subdomain })).toString('base64');
    passport.authenticate(role, { state })(req, rest, next);
  });

  router.get(
    '/callback',
    (req, res, next) => {
      const { state } = req.query;
      if (!state) {
        next(subdomainErrorMessage);
        return;
      }
      const parsedState = JSON.parse(Buffer.from(state as string, 'base64').toString());
      req.query.subdomain = parsedState.subdomain;
      passport.authorize(role)(req, res, next);
    },
    (req: any, res) => {
      res.send(req.account);
    },
  );
  return router;
};
