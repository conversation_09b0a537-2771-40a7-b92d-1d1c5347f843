import axios from 'axios';
import qs from 'querystring';
import moment from 'moment';
import { clientCredsConfig } from 'client-configs';
import { RudderCategory, roles } from '../../lib/constants';
import oAuthUtils from '../../lib/utils/oAuthUtils';

export default async (credentials: any, rudderCategory?: RudderCategory) => {
  const envKey = oAuthUtils.getEnvironmentVariableKey(
    roles.YANDEX_METRICA_OFFLINE_EVENTS,
    rudderCategory,
  );
  const { client_id, client_secret } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);

  const { refreshToken } = credentials;
  const credentialsString = `${client_id}:${client_secret}`;
  try {
    const response = await axios.post(
      `https://oauth.yandex.com/token`,
      qs.stringify({
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Basic ${Buffer.from(credentialsString).toString('base64')}`,
        },
      },
    );
    const { access_token, refresh_token, expires_in } = response.data;
    return {
      accessToken: access_token,
      refreshToken: refresh_token,
      expirationDate: moment().add(expires_in, 's').toISOString(),
    };
  } catch (err) {
    return oAuthUtils.handleInvalidGrantError(roles.YANDEX_METRICA_OFFLINE_EVENTS, err);
  }
};
