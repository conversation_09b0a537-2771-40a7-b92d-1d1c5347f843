import axios from 'axios';
import qs from 'querystring';
import { googleConfig } from 'client-configs';
import oAuthUtils from '../../lib/utils/oAuthUtils';

const { client_id, client_secret, developer_token } = oAuthUtils.getFromConfig<googleConfig>(
  `oauth.credentials.google_adwords`,
);

export default async (credentials: any) => {
  const response = await axios.post(
    'https://www.googleapis.com/oauth2/v3/token',
    qs.stringify({
      grant_type: 'refresh_token',
      refresh_token: credentials.refresh_token,
      client_id,
      client_secret,
    }),
  );

  const refreshed = response.data;
  refreshed.developer_token = developer_token;

  return refreshed;
};
