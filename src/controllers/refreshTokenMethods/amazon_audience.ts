import axios from 'axios';
import qs from 'querystring';
import { clientCredsConfig } from 'client-configs';
import { roles, RudderCategory } from '../../lib/constants';
import oAuthUtils from '../../lib/utils/oAuthUtils';

export default async (credentials: any, rudderCategory?: RudderCategory) => {
  const envKey = oAuthUtils.getEnvironmentVariableKey(roles.AMAZON_AUDIENCE, rudderCategory);
  const { client_id, client_secret } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);

  const { refreshToken } = credentials;
  const grant_type = 'refresh_token';
  const data = qs.stringify({
    grant_type,
    refresh_token: refreshToken,
    client_id,
    client_secret,
  });
  try {
    const response = await axios.post('https://api.amazon.com/auth/o2/token', data);
    if (response.status === 200) {
      const { access_token, refresh_token } = response.data;
      return { accessToken: access_token, refreshToken: refresh_token };
    }
    return response.data;
  } catch (err) {
    return oAuthUtils.handleInvalidGrantError(roles.AMAZON_AUDIENCE, err);
  }
};
