import { googleConfig } from 'client-configs';
import utils from '../../lib/utils/oAuthUtils';
import { roles } from '../../lib/constants';

const role = roles.BING_ADS;
const { client_id, client_secret, developer_token } = utils.getFromConfig<googleConfig>(
  `oauth.credentials.${role}`,
);
/**
 * Bing ads SDK requires clientId, clientSecret and developerToken to refresh auth tokens
 * We are overriding /token for role "bing_ads" to provide the neccesary tokens to blendo-integrations
 * This is a temporary implementation, as rudder-auth is not doing any token refresh for bing ads
 * Doc reference: https://docs.microsoft.com/en-us/advertising/guides/authentication-oauth-get-tokens?view=bingads-13
 */

export default () => ({
  client_id,
  client_secret,
  developer_token,
  redirect_url: utils.callbackURLByRole(role),
});
