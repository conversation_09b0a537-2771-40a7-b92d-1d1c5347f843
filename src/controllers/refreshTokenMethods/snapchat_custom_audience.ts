import axios from 'axios';
import { clientCredsConfig } from 'client-configs';
import { RudderCategory, roles } from '../../lib/constants';
import oAuthUtils from '../../lib/utils/oAuthUtils';

export default async (credentials: any, rudderCategory?: RudderCategory) => {
  const envKey = oAuthUtils.getEnvironmentVariableKey(
    roles.SNAPCHAT_CUSTOM_AUDIENCE,
    rudderCategory,
  );
  const { client_id, client_secret } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);

  const { refresh_token } = credentials;

  const response = await axios.post(
    'https://accounts.snapchat.com/login/oauth2/access_token',
    {},
    {
      params: {
        grant_type: 'refresh_token',
        refresh_token,
        client_id,
        client_secret,
      },
    },
  );
  if (response.status === 200) {
    const { access_token, refresh_token: refreshToken } = response.data;
    return { access_token, refresh_token: refreshToken };
  }
  return response.data;
};
