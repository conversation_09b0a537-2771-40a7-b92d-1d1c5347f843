import axios from 'axios';
import qs from 'querystring';
import moment from 'moment';
import { clientCredsConfig } from 'client-configs';
import oAuthUtils from '../../lib/utils/oAuthUtils';
import { roles } from '../../lib/constants';

const { client_id, client_secret } = oAuthUtils.getFromConfig<clientCredsConfig>(
  `oauth.credentials.${roles.HUBSPOT}`,
);

export default async (credentials: any) => {
  const { refresh_token } = credentials;

  try {
    const response = await axios.post(
      `https://api.hubapi.com/oauth/v1/token`,
      qs.stringify({
        grant_type: 'refresh_token',
        refresh_token,
        client_id,
        client_secret,
      }),
    );

    const { access_token, refresh_token: new_refresh_token, expires_in } = response?.data || {};
    return {
      accessToken: access_token,
      refreshToken: new_refresh_token,
      expirationDate: moment().add(expires_in, 's').toISOString(),
    };
  } catch (err) {
    console.log(err);
    throw err;
  }
};
