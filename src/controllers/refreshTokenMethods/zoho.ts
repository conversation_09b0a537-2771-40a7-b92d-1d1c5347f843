import { clientCredsConfig } from 'client-configs';
import axios from 'axios';
import { isDefinedAndNotNull } from '@rudderstack/integrations-lib';
import FormData from 'form-data';
import { getZohoDC } from '../../routes/auth/utils/zoho';
import { RudderCategory, roles } from '../../lib/constants';
import oAuthUtils from '../../lib/utils/oAuthUtils';

// ref : https://www.zoho.com/accounts/protocol/oauth/web-apps/access-token-expiry.html

export default async (credentials: any, rudderCategory?: RudderCategory) => {
  const envKey = oAuthUtils.getEnvironmentVariableKey(roles.ZOHO, rudderCategory);
  const scopes = [
    'ZohoCRM.settings.modules.ALL',
    'ZohoCRM.settings.ALL',
    'ZohoCRM.settings.fields.ALL',
    'aaaserver.profile.READ',
    'ZohoCRM.modules.ALL',
    'ZohoCRM.users.ALL',
    'ZohoCRM.coql.READ',
  ];
  const { client_id, client_secret } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);

  const { refreshToken } = credentials;
  const form = new FormData();
  form.append('client_id', client_id);
  form.append('client_secret', client_secret);
  form.append('refresh_token', refreshToken);
  form.append('grant_type', 'refresh_token');
  form.append('scope', scopes.join(' ')); // added this after QA as refresh token was giving invalid oauth scope error when upserting records

  try {
    const zohoDcURL = getZohoDC();
    const response = await axios.post(`${zohoDcURL}/oauth/v2/token`, form, {});
    const { access_token, error } = response.data;
    if (isDefinedAndNotNull(error)) {
      return oAuthUtils.handleInvalidGrantError(roles.ZOHO, error);
    }
    return {
      accessToken: access_token,
    };
  } catch (err) {
    return oAuthUtils.handleInvalidGrantError(roles.ZOHO, err);
  }
};
