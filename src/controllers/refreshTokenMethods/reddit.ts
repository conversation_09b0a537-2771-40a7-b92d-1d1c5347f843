import axios from 'axios';
import qs from 'querystring';
import { clientCredsConfig } from 'client-configs';
import { roles, RudderCategory } from '../../lib/constants';
import oAuthUtils from '../../lib/utils/oAuthUtils';

export default async (credentials: any, rudderCategory?: RudderCategory) => {
  const envKey = oAuthUtils.getEnvironmentVariableKey(roles.REDDIT, rudderCategory);
  const { client_id, client_secret } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);

  const credentialsString = `${client_id}:${client_secret}`;
  const encodedCredentials = Buffer.from(credentialsString).toString('base64');
  const { refreshToken } = credentials;
  const grant_type = 'refresh_token';
  const data = qs.stringify({
    grant_type,
    refresh_token: refreshToken,
  });
  try {
    const response = await axios.post('https://www.reddit.com/api/v1/access_token', data, {
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
      },
    });
    if (response.status === 200) {
      const { access_token, refresh_token } = response.data;
      return { accessToken: access_token, refreshToken: refresh_token };
    }
    return response.data;
  } catch (err) {
    return oAuthUtils.handleInvalidGrantError(roles.REDDIT, err);
  }
};
