import axios from 'axios';
import qs from 'querystring';
import { clientCredsConfig } from 'client-configs';
import { roles, RudderCategory } from '../../lib/constants';
import oAuthUtils from '../../lib/utils/oAuthUtils';

export default async (credentials: any, rudderCategory?: RudderCategory) => {
  const envKey = oAuthUtils.getEnvironmentVariableKey(roles.CRITEO_AUDIENCE, rudderCategory);
  const { client_id, client_secret } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);

  const { refreshToken } = credentials;
  const grant_type = 'refresh_token';
  const data = qs.stringify({
    grant_type,
    refresh_token: refreshToken,
    client_id,
    client_secret,
  });
  try {
    const response = await axios.post('https://api.criteo.com/oauth2/token', data, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    if (response.status === 200) {
      const { access_token, refresh_token } = response.data;
      return { accessToken: access_token, refreshToken: refresh_token };
    }
    return response.data;
  } catch (err) {
    // API Doc: https://developers.criteo.com/marketing-solutions/docs/api-error-types
    return oAuthUtils.handleInvalidGrantError(roles.CRITEO_AUDIENCE, err);
  }
};
