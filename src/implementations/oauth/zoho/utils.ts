export type RegionKeys = 'US' | 'AU' | 'EU' | 'IN' | 'CN' | 'JP' | 'CA';

export const DATA_CENTRE_BASE_ENDPOINTS_MAP: Record<RegionKeys, string> = {
  US: 'https://accounts.zoho.com',
  AU: 'https://accounts.zoho.com.au',
  EU: 'https://accounts.zoho.eu',
  IN: 'https://accounts.zoho.in',
  CN: 'https://accounts.zoho.com.cn',
  JP: 'https://accounts.zoho.jp',
  CA: 'https://accounts.zohocloud.ca',
};

export const getZohoRegion = (region?: string): RegionKeys => {
  if (!region) {
    return 'US';
  }

  if (typeof region !== 'string') {
    throw new Error('Region must be a string');
  }

  const normalizedRegion = region.trim().toUpperCase() as RegionKeys;

  if (!DATA_CENTRE_BASE_ENDPOINTS_MAP[normalizedRegion]) {
    throw new Error(
      `Invalid Zoho region: ${region}. Valid regions are: ${Object.keys(DATA_CENTRE_BASE_ENDPOINTS_MAP).join(', ')}`,
    );
  }

  return normalizedRegion;
};

export const getZohoDC = (region?: string): string => {
  const zohoRegion = getZohoRegion(region);
  return DATA_CENTRE_BASE_ENDPOINTS_MAP[zohoRegion];
};
