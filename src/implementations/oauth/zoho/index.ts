import passport from 'passport';
import utils from '../../../lib/utils/oAuthUtils';
import { OAuthImplementation, AuthPayload, AccountDefinition } from '../../../types/oauth';
import { DATA_CENTRE_BASE_ENDPOINTS_MAP, getZohoDC, getZohoRegion } from './utils';

const ZohoCRMStrategy = require('passport-zoho-crm').Strategy;

export class ZohoImplementation implements OAuthImplementation {
  private strategyName: string;

  private scope: string[];

  private clientID: string;

  private clientSecret: string;

  private callbackURL: string;

  constructor(accountDefinition: AccountDefinition) {
    this.strategyName = utils.getOAuthImplementationIdentifier(accountDefinition);
    const { type, category } = accountDefinition;
    const { clientID, clientSecret, callbackURL } = utils.getOAuthStrategyOptionsWithStaticCallback(
      type,
      category,
    );

    if (!clientID || !clientSecret || !callbackURL) {
      throw new Error('Invalid OAuth configuration: missing clientID, clientSecret or callbackURL');
    }

    this.clientID = clientID;
    this.clientSecret = clientSecret;
    this.callbackURL = callbackURL;
    this.scope = [
      'ZohoCRM.settings.modules.ALL',
      'ZohoCRM.settings.ALL',
      'ZohoCRM.settings.fields.ALL',
      'ZohoCRM.users.ALL',
      'ZohoCRM.modules.ALL',
      'ZohoCRM.coql.READ',
      'aaaserver.profile.READ',
    ];

    this.initializeStrategies();
  }

  private initializeStrategies(): void {
    // Register passport strategy for each data center
    Object.keys(DATA_CENTRE_BASE_ENDPOINTS_MAP).forEach((region) => {
      const authServer = getZohoDC(region);
      const strategyNameWithRegion = `${this.strategyName}_${region}`;

      passport.use(
        strategyNameWithRegion,
        new ZohoCRMStrategy(
          {
            clientID: this.clientID,
            clientSecret: this.clientSecret,
            callbackURL: this.callbackURL,
            authorizationURL: `${authServer}/oauth/v2/auth?prompt=consent&access_type=offline`,
            tokenURL: `${authServer}/oauth/v2/token`,
            userProfileURL: `${authServer}/oauth/user/info`,
            scope: this.scope,
            response_type: 'code',
          },
          (accessToken: string, refreshToken: string, profile: any, callback: any) => {
            utils.handleCallbackV1(callback, {
              profile,
              secret: { accessToken, refreshToken },
            });
          },
        ),
      );
    });
  }

  getAuthMiddleware() {
    return (req: any, res: any, next: any) => {
      const payload: AuthPayload = req.body;
      if (!payload?.account) {
        throw new Error('Missing account information in request');
      }

      const { account } = payload;
      account.options = account.options || {};
      const { region } = account.options;

      this.validateZohoRegion(region);

      const strategyNameWithRegion = `${this.strategyName}_${region || 'US'}`;

      return passport.authenticate(strategyNameWithRegion)(req, res, next);
    };
  }

  getCallbackMiddleware() {
    return (req: any, res: any, next: any) => {
      const payload: AuthPayload = req.body;
      if (!payload?.account) {
        throw new Error('Missing account information in request');
      }

      const { account } = payload;
      account.options = account.options || {};
      const { region } = account.options;

      this.validateZohoRegion(region);

      const strategyNameWithRegion = `${this.strategyName}_${region || 'US'}`;

      return passport.authorize(strategyNameWithRegion)(req, res, next);
    };
  }

  async refreshToken(refreshToken: string, options: Record<string, any>) {
    if (!refreshToken) {
      throw new Error('Refresh token is required');
    }

    const region = options?.region || 'US';
    this.validateZohoRegion(region);

    const authServer = getZohoDC(region);
    const response = await fetch(`${authServer}/oauth/v2/token`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: this.clientID,
        client_secret: this.clientSecret,
      }),
    });

    if (!response.ok) {
      const errorData = (await response.json()) as { error_description?: string; error?: string };
      switch (response.status) {
        case 401:
          throw new Error(
            errorData.error_description || `Invalid refresh token: ${JSON.stringify(errorData)} `,
          );
        default:
          throw new Error(
            errorData.error_description || `Failed to refresh token: ${JSON.stringify(errorData)}`,
          );
      }
    }

    const data = (await response.json()) as {
      access_token: string;
      refresh_token: string;
      expires_in: number;
      scope: string;
      token_type: string;
    };

    if (!data.access_token) {
      console.error(
        `Failed to refresh token: access token not found in response ${JSON.stringify(data)}`,
      );
      return {
        error: {
          message: 'Failed to refresh token: access token not found in response',
        },
        data,
      };
    }

    return {
      secret: {
        accessToken: data.access_token,
        refreshToken: data.refresh_token,
        expires_in: data.expires_in,
        scope: data.scope,
        token_type: data.token_type,
      },
    };
  }

  // eslint-disable-next-line class-methods-use-this
  validateZohoRegion(region: unknown): void {
    if (typeof region !== 'string') {
      throw new Error('Region must be a string');
    }
    const normalizedRegion = getZohoRegion(region);
    if (!Object.prototype.hasOwnProperty.call(DATA_CENTRE_BASE_ENDPOINTS_MAP, normalizedRegion)) {
      throw new Error(`Invalid Zoho region: ${region}`);
    }
  }
}
