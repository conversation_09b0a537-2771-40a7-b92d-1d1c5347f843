import { ZohoImplementation } from './index';
import utils from '../../../lib/utils/oAuthUtils';
import { AccountDefinition } from '../../../types/oauth';
import passport from 'passport';
import { NextFunction } from 'express';

jest.mock('passport');
jest.mock('../../../../src/lib/utils/oAuthUtils');

describe('ZohoImplementation', () => {
  const mockAccountDefinition: AccountDefinition = {
    type: 'zoho',
    category: 'destination',
    name: 'oauth',
    authenticationType: 'oauth',
  };

  const mockOAuthOptions = {
    clientID: 'test-client-id',
    clientSecret: 'test-client-secret',
    callbackURL: 'http://test-callback',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (utils.getOAuthStrategyOptionsWithStaticCallback as jest.Mock).mockReturnValue(
      mockOAuthOptions,
    );
    (utils.getOAuthImplementationIdentifier as jest.Mock).mockReturnValue(
      [
        mockAccountDefinition.category,
        mockAccountDefinition.type,
        mockAccountDefinition.authenticationType,
      ].join('_'),
    );
  });

  describe('constructor', () => {
    it('should initialize with valid configuration', () => {
      const implementation = new ZohoImplementation(mockAccountDefinition);
      expect(implementation).toBeDefined();
      expect(utils.getOAuthStrategyOptionsWithStaticCallback).toHaveBeenCalledWith(
        'zoho',
        'destination',
      );
    });

    it('should throw error with invalid configuration', () => {
      (utils.getOAuthStrategyOptionsWithStaticCallback as jest.Mock).mockReturnValue({});
      expect(() => new ZohoImplementation(mockAccountDefinition)).toThrow(
        'Invalid OAuth configuration: missing clientID, clientSecret or callbackURL',
      );
    });
  });

  describe('refreshToken', () => {
    let implementation: ZohoImplementation;
    const mockRefreshToken = 'test-refresh-token';
    const mockResponse = {
      access_token: 'new-access-token',
      refresh_token: 'new-refresh-token',
      expires_in: 3600,
      scope: 'test-scope',
      token_type: 'Bearer',
    };

    beforeEach(() => {
      implementation = new ZohoImplementation(mockAccountDefinition);
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });
    });

    it('should successfully refresh token for US region', async () => {
      const result = await implementation.refreshToken(mockRefreshToken, { region: 'US' });

      expect(fetch).toHaveBeenCalledWith('https://accounts.zoho.com/oauth/v2/token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: expect.any(URLSearchParams),
      });

      expect(result).toEqual({
        secret: {
          accessToken: mockResponse.access_token,
          refreshToken: mockResponse.refresh_token,
          expires_in: mockResponse.expires_in,
          scope: mockResponse.scope,
          token_type: mockResponse.token_type,
        },
      });
    });

    it('should successfully refresh token for EU region', async () => {
      const result = await implementation.refreshToken(mockRefreshToken, { region: 'EU' });

      expect(fetch).toHaveBeenCalledWith('https://accounts.zoho.eu/oauth/v2/token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: expect.any(URLSearchParams),
      });
      expect(result).toEqual({
        secret: {
          accessToken: mockResponse.access_token,
          refreshToken: mockResponse.refresh_token,
          expires_in: mockResponse.expires_in,
          scope: mockResponse.scope,
          token_type: mockResponse.token_type,
        },
      });
    });

    it('should throw error for invalid region', async () => {
      await expect(
        implementation.refreshToken(mockRefreshToken, { region: 'INVALID' }),
      ).rejects.toThrow('Invalid Zoho region');
    });

    it('should throw error when refresh token is missing', async () => {
      await expect(implementation.refreshToken('', { region: 'US' })).rejects.toThrow(
        'Refresh token is required',
      );
    });

    it('should handle refresh token error response', async () => {
      const errorResponse = {
        ok: false,
        status: 400,
        json: () => Promise.resolve({ error: 'invalid_grant' }),
      };
      global.fetch = jest.fn().mockResolvedValue(errorResponse);

      await expect(implementation.refreshToken(mockRefreshToken, { region: 'US' })).rejects.toThrow(
        'Failed to refresh token',
      );
    });
  });

  describe('initializeStrategies', () => {
    it('should initialize passport strategies for all regions', () => {
      new ZohoImplementation(mockAccountDefinition);
      expect(passport.use).toHaveBeenCalledTimes(7); // One for each supported region
    });
  });

  describe('getAuthMiddleware', () => {
    let implementation: ZohoImplementation;
    let mockReq: any;
    let mockRes: any;
    let mockNext: NextFunction;
    const mockAuthenticateMiddleware = jest.fn();

    beforeEach(() => {
      implementation = new ZohoImplementation(mockAccountDefinition);
      mockReq = {
        body: {
          account: {
            options: {
              region: 'US',
            },
          },
        },
      };
      mockRes = {};
      mockNext = jest.fn();
      (passport.authenticate as jest.Mock).mockReturnValue(mockAuthenticateMiddleware);
    });

    it('should return middleware function', () => {
      const middleware = implementation.getAuthMiddleware();
      expect(typeof middleware).toBe('function');
    });

    it('should call passport.authenticate with correct strategy name for US region', () => {
      const middleware = implementation.getAuthMiddleware();

      middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(passport.authenticate).toHaveBeenCalledWith('destination_zoho_oauth_US');
      expect(mockAuthenticateMiddleware).toHaveBeenCalledWith(mockReq, mockRes, mockNext);
    });

    it('should call passport.authenticate with correct strategy name for EU region', () => {
      const middleware = implementation.getAuthMiddleware();

      middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(passport.authenticate).toHaveBeenCalledWith('destination_zoho_oauth_US');
      expect(mockAuthenticateMiddleware).toHaveBeenCalledWith(mockReq, mockRes, mockNext);
    });

    it('should use default US region when region is not specified', () => {
      const middleware = implementation.getAuthMiddleware();

      middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(passport.authenticate).toHaveBeenCalledWith('destination_zoho_oauth_US');
      expect(mockAuthenticateMiddleware).toHaveBeenCalledWith(mockReq, mockRes, mockNext);
    });

    it('should throw error for invalid region', () => {
      const middleware = implementation.getAuthMiddleware();

      mockReq.body.account.options.region = 'INVALID';

      expect(() => {
        middleware(mockReq as Request, mockRes as Response, mockNext);
      }).toThrow('Invalid Zoho region');

      expect(passport.authenticate).not.toHaveBeenCalled();
      expect(mockAuthenticateMiddleware).not.toHaveBeenCalled();
    });
  });

  describe('getCallbackMiddleware', () => {
    let implementation: ZohoImplementation;
    let mockReq: any;
    let mockRes: any;
    let mockNext: NextFunction;
    const mockAuthorizeMiddleware = jest.fn();

    beforeEach(() => {
      implementation = new ZohoImplementation(mockAccountDefinition);
      mockReq = {
        body: {
          account: {
            options: {
              region: 'US',
            },
          },
        },
      };
      mockRes = {};
      mockNext = jest.fn();
      (passport.authorize as jest.Mock).mockReturnValue(mockAuthorizeMiddleware);
    });

    it('should return middleware function', () => {
      const middleware = implementation.getCallbackMiddleware();
      expect(typeof middleware).toBe('function');
    });

    it('should call passport.authorize with correct strategy name for US region', () => {
      const middleware = implementation.getCallbackMiddleware();

      middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(passport.authorize).toHaveBeenCalledWith('destination_zoho_oauth_US');
      expect(mockAuthorizeMiddleware).toHaveBeenCalledWith(mockReq, mockRes, mockNext);
    });

    it('should call passport.authorize with correct strategy name for EU region', () => {
      const middleware = implementation.getCallbackMiddleware();
      mockReq.body.account.options.region = 'EU';

      middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(passport.authorize).toHaveBeenCalledWith('destination_zoho_oauth_EU');
      expect(mockAuthorizeMiddleware).toHaveBeenCalledWith(mockReq, mockRes, mockNext);
    });
  });
});
