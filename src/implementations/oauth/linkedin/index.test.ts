import { LinkedInImplementation } from './index';
import utils from '../../../lib/utils/oAuthUtils';
import { AccountDefinition } from '../../../types/oauth';
import passport from 'passport';
import { NextFunction } from 'express';

jest.mock('passport');
jest.mock('../../../lib/utils/oAuthUtils');

describe('LinkedInImplementation', () => {
  const mockAccountDefinition: AccountDefinition = {
    type: 'linkedin',
    category: 'destination',
    name: 'oauth',
    authenticationType: 'oauth',
  };

  const mockOAuthOptions = {
    clientID: 'test-client-id',
    clientSecret: 'test-client-secret',
    callbackURL: 'http://test-callback',
  };

  const mockScope = ['r_liteprofile', 'r_ads'];
  const mockProfileFields = ['formatted-name', 'email-address'];
  const mockOptions = { profileFields: mockProfileFields };

  beforeEach(() => {
    jest.clearAllMocks();
    (utils.getOAuthStrategyOptionsWithStaticCallback as jest.Mock).mockReturnValue(
      mockOAuthOptions,
    );
    (utils.getOAuthImplementationIdentifier as jest.Mock).mockReturnValue(
      [
        mockAccountDefinition.category,
        mockAccountDefinition.type,
        mockAccountDefinition.authenticationType,
      ].join('_'),
    );
  });

  describe('constructor', () => {
    it('should initialize with valid configuration', () => {
      const implementation = new LinkedInImplementation(
        mockAccountDefinition,
        mockScope,
        mockOptions,
      );
      expect(implementation).toBeDefined();
      expect(utils.getOAuthStrategyOptionsWithStaticCallback).toHaveBeenCalledWith(
        'linkedin',
        'destination',
      );
    });

    it('should throw error with invalid configuration', () => {
      (utils.getOAuthStrategyOptionsWithStaticCallback as jest.Mock).mockReturnValue({});
      expect(
        () => new LinkedInImplementation(mockAccountDefinition, mockScope, mockOptions),
      ).toThrow('Invalid OAuth configuration: missing clientID, clientSecret or callbackURL');
    });

    it('should throw error when scope is not provided', () => {
      expect(
        () => new LinkedInImplementation(mockAccountDefinition, undefined, mockOptions),
      ).toThrow('Invalid OAuth configuration: missing scope');
    });
  });

  describe('refreshToken', () => {
    let implementation: LinkedInImplementation;
    const mockRefreshToken = 'test-refresh-token';
    const mockResponse = {
      access_token: 'new-access-token',
      refresh_token: 'new-refresh-token',
      expires_in: 3600,
    };

    beforeEach(() => {
      implementation = new LinkedInImplementation(mockAccountDefinition, mockScope, mockOptions);
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });
    });

    it('should successfully refresh token', async () => {
      const result = await implementation.refreshToken(mockRefreshToken);

      expect(fetch).toHaveBeenCalledWith('https://www.linkedin.com/oauth/v2/accessToken', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: expect.any(URLSearchParams),
      });

      expect(result).toEqual({
        accessToken: mockResponse.access_token,
        refreshToken: mockResponse.refresh_token,
        expirationDate: expect.any(String),
      });
    });

    it('should throw error when refresh token is missing', async () => {
      await expect(implementation.refreshToken('')).rejects.toThrow('Refresh token is required');
    });

    it('should handle refresh token error response', async () => {
      const errorResponse = {
        ok: false,
        status: 401,
        json: () => Promise.resolve({ error: 'invalid_grant' }),
      };
      global.fetch = jest.fn().mockResolvedValue(errorResponse);

      await expect(implementation.refreshToken(mockRefreshToken)).rejects.toThrow(
        'Invalid refresh token',
      );
    });

    it('should handle general refresh token error', async () => {
      const errorResponse = {
        ok: false,
        status: 500,
        json: () => Promise.resolve({ error: 'server_error' }),
      };
      global.fetch = jest.fn().mockResolvedValue(errorResponse);

      await expect(implementation.refreshToken(mockRefreshToken)).rejects.toThrow(
        'Failed to refresh token',
      );
    });
  });

  describe('initializeStrategies', () => {
    it('should initialize passport strategy', () => {
      new LinkedInImplementation(mockAccountDefinition, mockScope, mockOptions);
      expect(passport.use).toHaveBeenCalledTimes(1);
      expect(passport.use).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          name: 'linkedin',
        }),
      );
    });
  });

  describe('getAuthMiddleware', () => {
    let implementation: LinkedInImplementation;
    let mockReq: any;
    let mockRes: any;
    let mockNext: NextFunction;
    const mockAuthenticateMiddleware = jest.fn();

    beforeEach(() => {
      implementation = new LinkedInImplementation(mockAccountDefinition, mockScope, mockOptions);
      mockReq = {
        body: {
          account: {
            options: {
              someOption: 'someValue',
            },
          },
        },
      };
      mockRes = {};
      mockNext = jest.fn();
      (passport.authenticate as jest.Mock).mockReturnValue(mockAuthenticateMiddleware);
    });

    it('should return middleware function', () => {
      const middleware = implementation.getAuthMiddleware();
      expect(typeof middleware).toBe('function');
    });

    it('should call passport.authenticate with correct strategy name', () => {
      const middleware = implementation.getAuthMiddleware();
      middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(passport.authenticate).toHaveBeenCalledWith('destination_linkedin_oauth');
      expect(mockAuthenticateMiddleware).toHaveBeenCalledWith(mockReq, mockRes, mockNext);
    });
  });

  describe('getCallbackMiddleware', () => {
    let implementation: LinkedInImplementation;
    let mockReq: any;
    let mockRes: any;
    let mockNext: NextFunction;
    const mockAuthorizeMiddleware = jest.fn();

    beforeEach(() => {
      implementation = new LinkedInImplementation(mockAccountDefinition, mockScope, mockOptions);
      mockReq = {
        body: {
          account: {
            options: {
              someOption: 'someValue',
            },
          },
        },
      };
      mockRes = {};
      mockNext = jest.fn();
      (passport.authorize as jest.Mock).mockReturnValue(mockAuthorizeMiddleware);
    });

    it('should return middleware function', () => {
      const middleware = implementation.getCallbackMiddleware();
      expect(typeof middleware).toBe('function');
    });

    it('should call passport.authorize with correct strategy name', () => {
      const middleware = implementation.getCallbackMiddleware();
      middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(passport.authorize).toHaveBeenCalledWith('destination_linkedin_oauth');
      expect(mockAuthorizeMiddleware).toHaveBeenCalledWith(mockReq, mockRes, mockNext);
    });
  });
});
