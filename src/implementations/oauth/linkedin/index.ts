import passport from 'passport';
import utils from '../../../lib/utils/oAuthUtils';
import { AccountDefinition, OAuthImplementation } from '../../../types/oauth';

const LinkedInStrategy = require('passport-linkedin-oauth2').Strategy;

export class LinkedInImplementation implements OAuthImplementation {
  private strategyName: string;

  private scope: string[];

  private clientID: string;

  private clientSecret: string;

  private callbackURL: string;

  private profileFields: string[];

  constructor(accountDefinition: AccountDefinition, scope?: string[], options?: any) {
    this.strategyName = utils.getOAuthImplementationIdentifier(accountDefinition);
    const { type, category } = accountDefinition;
    const { clientID, clientSecret, callbackURL } = utils.getOAuthStrategyOptionsWithStaticCallback(
      type,
      category,
    );

    if (!clientID || !clientSecret || !callbackURL) {
      throw new Error('Invalid OAuth configuration: missing clientID, clientSecret or callbackURL');
    }

    if (!scope) {
      throw new Error('Invalid OAuth configuration: missing scope');
    }

    this.clientID = clientID;
    this.clientSecret = clientSecret;
    this.callbackURL = callbackURL;
    this.scope = scope;
    this.profileFields = options.profileFields;

    this.initializeStrategies();
  }

  private initializeStrategies() {
    passport.use(
      this.strategyName,
      new LinkedInStrategy(
        {
          clientID: this.clientID,
          clientSecret: this.clientSecret,
          callbackURL: this.callbackURL,
          scope: this.scope,
          profileFields: this.profileFields,
        },
        (accessToken: string, refreshToken: string, profile: any, callback: any) => {
          utils.handleCallbackV1(callback, {
            profile,
            secret: {
              accessToken,
              refreshToken,
              expirationDate: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // LinkedIn tokens expire in 60 minutes
            },
          });
        },
      ),
    );
  }

  getAuthMiddleware() {
    return (req: any, res: any, next: any) =>
      passport.authenticate(this.strategyName)(req, res, next);
  }

  getCallbackMiddleware() {
    return (req: any, res: any, next: any) => passport.authorize(this.strategyName)(req, res, next);
  }

  async refreshToken(refreshToken: string) {
    if (!refreshToken) {
      throw new Error('Refresh token is required');
    }

    const response = await fetch('https://www.linkedin.com/oauth/v2/accessToken', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: this.clientID,
        client_secret: this.clientSecret,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      switch (response.status) {
        case 401:
          throw new Error(`Invalid refresh token: ${JSON.stringify(errorData)}`);
        default:
          throw new Error(`Failed to refresh token: ${JSON.stringify(errorData)}`);
      }
    }

    const data = (await response.json()) as {
      access_token: string;
      refresh_token: string;
      expires_in: number;
    };

    if (!data.access_token) {
      console.error(
        `Failed to refresh token: access token not found in response ${JSON.stringify(data)}`,
      );
      return {
        error: {
          message: 'Failed to refresh token: access token not found in response',
        },
        data,
      };
    }

    return {
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      expirationDate: new Date(Date.now() + data.expires_in * 1000).toISOString(),
    };
  }
}
