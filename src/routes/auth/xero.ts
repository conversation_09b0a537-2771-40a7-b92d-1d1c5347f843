import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';
import { roles } from '../../lib/constants';

const XeroStrategy = require('passport-xero-oauth2');

const router: express.Router = express.Router();
const role = roles.XERO;

const scope = [
  'offline_access',
  'accounting.transactions.read',
  'accounting.reports.read',
  'accounting.journals.read',
  'accounting.settings.read',
  'accounting.contacts.read',
  'accounting.attachments.read',
  'files.read',
  'assets.read',
  'projects.read',
  'openid',
  'email',
  'profile',
];

const clientOptions = utils.oauthStrategyOptions(role);
passport.use(
  role,
  new XeroStrategy(
    {
      ...clientOptions,
      state: false,
    },
    (
      accessToken: string,
      refreshToken: string,
      extra: any,
      profile: any,
      callback: TSPassportCallback,
    ) => {
      utils.handleCallback(callback, {
        role,
        profile,
        secret: { accessToken, refreshToken },
      });
    },
  ),
);

// setup authentication and callback routes
router.get('/authz', (req, res, d) => {
  // @ts-expect-error: request object doesn't contain session object
  req.session = {};
  return passport.authenticate(role, {
    scope,
  })(req, res, d);
});

router.get(
  '/callback',
  (req, res, d) => {
    // @ts-expect-error: request object doesn't contain session object
    req.session = { authParams: { scope } };
    return passport.authorize(role)(req, res, d);
  },
  (req: any, res) => {
    res.send(req.account);
  },
);

export default router;
