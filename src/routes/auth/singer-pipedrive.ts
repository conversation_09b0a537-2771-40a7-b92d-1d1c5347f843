import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import { URL } from 'url';
import utils from '../../lib/utils/oAuthUtils';
import { roles } from '../../lib/constants';

const role = roles.SINGER_PIPEDRIVE;

const router: express.Router = express.Router();

const PipedriveStrategy = require('../../lib/passport/pipedrive').Strategy;

passport.use(
  role,
  new PipedriveStrategy(
    {
      ...utils.oauthStrategyOptions(role),
      callbackURL: utils.callbackURLByRole(role),
    },
    (
      accessToken: string,
      refreshToken: string,
      details: any,
      profile: any,
      callback: TSPassportCallback,
    ) => {
      const { api_domain } = details;
      const accountName = api_domain ? new URL(api_domain).hostname : 'pipedrive.com';
      utils.handleCallback(callback, {
        renderAccountInfo: () => ({
          accountName,
          metadata: { userId: accountName },
        }),
        role,
        profile,
        secret: { accessToken, refreshToken },
      });
    },
  ),
);

// setup authentication and callback routes
router.get('/authz', passport.authenticate(role, {}));

router.get(
  '/callback',
  (req, res, next) => passport.authorize(role)(req, res, next),
  (req: any, res) => {
    res.send(req.account);
  },
);

export default router;
