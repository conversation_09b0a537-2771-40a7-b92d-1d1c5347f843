import express from 'express';
import passport from 'passport';
import moment from 'moment';
import { roles, RudderCategory } from '../../lib/constants';
import utils from '../../lib/utils/oAuthUtils';

export type TSPassportCallback = (error: any, response: any) => any;

const router: express.Router = express.Router();
const role = roles.YANDEX_METRICA_OFFLINE_EVENTS;
const YandexMetricaStrategy = require('passport-yandex').Strategy;

Object.values(RudderCategory).forEach((rudderCategory) => {
  const strategyName = utils.getStrategyName(role, rudderCategory);
  const opts = utils.oauthStrategyOptions(role, rudderCategory);
  passport.use(
    strategyName,
    new YandexMetricaStrategy(
      opts,
      (
        access_token: string,
        refresh_token: string,
        response: any,
        profile: any,
        callback: TSPassportCallback,
      ) => {
        utils.handleCallback(callback, {
          role,
          profile,
          secret: {
            accessToken: access_token,
            refreshToken: refresh_token,
            expirationDate: moment().add(response.expires_in, 's').toISOString(),
          },
        });
      },
    ),
  );
});

const authzHandler = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  return passport.authenticate(strategyName)(req, res, next);
};
const callbackHandler = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction,
) => {
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  return passport.authorize(strategyName)(req, res, next);
};
router.get('/authz', authzHandler);
router.get('/callback', callbackHandler, (req: any, res) => {
  res.send(req.account);
});
export default router;
