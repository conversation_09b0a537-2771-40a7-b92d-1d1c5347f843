import express from 'express';
import passport from 'passport';
import { roles, RudderCategory } from '../../lib/constants';
import utils from '../../lib/utils/oAuthUtils';
import { DATA_CENTRE_BASE_ENDPOINTS_MAP, getZohoDC, getZohoRegion } from './utils/zoho';

// eslint-disable-next-line import/no-extraneous-dependencies
const ZohoCRMStrategy = require('passport-zoho-crm').Strategy;

const router: express.Router = express.Router();
const role = roles.ZOHO;
export type StrategyNameParams = {
  region: string;
  category: RudderCategory;
};

export const getStrategyNameWithRegion = (category: string, region: string = 'US'): string => {
  const strategyName = utils.getStrategyName(role, category);
  const zohoRegion = getZohoRegion(region);
  return [strategyName, zohoRegion].join('__');
};

// ref:  https://www.zoho.com/accounts/protocol/oauth/web-apps/authorization.html
Object.values(RudderCategory).forEach((category) => {
  Object.keys(DATA_CENTRE_BASE_ENDPOINTS_MAP).forEach((region) => {
    const strategyName = getStrategyNameWithRegion(category, region);
    const authServer = getZohoDC(region);
    passport.use(
      strategyName,
      new ZohoCRMStrategy(
        {
          ...utils.oauthStrategyOptions(role, RudderCategory.Destination),
          authorizationURL: `${authServer}/oauth/v2/auth?prompt=consent&access_type=offline`,
          tokenURL: `${authServer}/oauth/v2/token`,
          userProfileURL: `${authServer}/oauth/user/info`,
          scope: [
            'ZohoCRM.settings.modules.ALL',
            'ZohoCRM.settings.ALL',
            'ZohoCRM.settings.fields.ALL',
            'aaaserver.profile.READ',
            'ZohoCRM.users.ALL',
            'ZohoCRM.modules.ALL',
            'ZohoCRM.coql.READ',
          ],
          response_type: 'code',
        },
        (accessToken: any, refreshToken: any, profile: any, cb: any) => {
          // handle callback
          utils.handleCallback(cb, {
            role,
            profile,
            secret: { accessToken, refreshToken },
          });
        },
      ),
    );
  });
});

export const authzHandler = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction,
) => {
  const strategyName = getStrategyNameWithRegion(req.rudderCategory || '');
  return passport.authenticate(strategyName)(req, res, next);
};
export const callbackHandler = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction,
) => {
  const strategyName = getStrategyNameWithRegion(req.rudderCategory || '');
  return passport.authorize(strategyName)(req, res, next);
};
router.get('/authz', authzHandler);
router.get('/callback', callbackHandler, (req: any, res) => {
  res.send(req.account);
});

export default router;
