import { roles } from '../../lib/constants';
import hubspot from '../../controllers/oauthImplementations/hubspot';

const optional_scope = [
  'crm.schemas.line_items.read',
  'crm.objects.line_items.read',
  'crm.schemas.deals.read',
  'crm.objects.deals.read',
  'crm.schemas.companies.read',
  'crm.objects.companies.read',
  'crm.objects.feedback_submissions.read',
  'crm.objects.quotes.read',
  'crm.schemas.quotes.read',
  'crm.objects.owners.read',
  'crm.lists.read',
  'content',
  'forms',
  'tickets',
  'e-commerce',
  'sales-email-read',
  'automation',
];
export default hubspot(roles.HUBSPOT, optional_scope.join(' '));
