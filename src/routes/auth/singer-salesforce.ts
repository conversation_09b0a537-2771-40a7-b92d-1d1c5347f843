/*
    For Blendo salesforce we support two kind of sources salesforce and salesforce_sandbox. 
    For singer-salesforce if the account is sandbox or not is defined in source-configuration using is_sandbox property
    that is sent as query parameter in the OAuth request. This change is about reading the is_sandbox parameter
    and use the correct passport strategy accordingly.
    Notion ticket: https://www.notion.so/rudderstacks/d8ea6e5cd7b2481db207d6b239676688?v=a417c150dc2f4f989102ba3fbb4986fc&p=3bcc96f651704dea813db3db5f7500fc
*/

import express from 'express';
import passport from 'passport';
import { SalesforceRoles } from 'auth';
import salesforce from '../../controllers/oauthImplementations/salesforce';
import { roles } from '../../lib/constants';

const role = roles.SINGER_SALESFORCE as SalesforceRoles;
const sandboxRole = roles.SINGER_SALESFORCE_SANDBOX as SalesforceRoles;

salesforce({ role }, false);
salesforce(
  {
    role: sandboxRole,
    oauthStrategyOptionsRole: role,
    accountRole: role,
    isSandbox: true,
    accountNameSuffix: ' - sandbox',
    sandboxCallbackURLByRole: role,
  },
  false,
);

const router: express.Router = express.Router();
const sandboxState = Buffer.from(JSON.stringify({ is_sandbox: 'true' })).toString('base64');

router.get('/authz', (req, rest, next) => {
  const { is_sandbox = 'false' } = req.query;
  let authRole = role;
  let state: string | undefined;
  // check if the account is sandbox
  if (is_sandbox === 'true') {
    // use singer-salesforce-sandbox role instead
    authRole = sandboxRole;
    // set to state { is_sandbox: "true" } to be passed in the callback
    state = sandboxState;
  }
  // save is_sandbox value in the auth state to be able to retrieve it in the callback
  passport.authenticate(authRole, { state })(req, rest, next);
});

router.get(
  '/callback',
  (req, res, next) => {
    let authRole = role;
    const { state } = req.query;
    let is_sandbox = 'false';
    if (state) {
      // parse the state to get the is_sandbox value
      const parsedState = JSON.parse(Buffer.from(state as string, 'base64').toString());
      is_sandbox = parsedState?.is_sandbox;
    }
    if (is_sandbox === 'true') {
      // use singer-salesforce-sandbox role instead
      authRole = sandboxRole;
    }
    passport.authorize(authRole)(req, res, next);
  },
  (req: any, res) => {
    res.send(req.account);
  },
);
export default router;
