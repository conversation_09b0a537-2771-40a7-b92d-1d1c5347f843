import request from 'supertest';
import express from 'express';
import { AuthPayload } from '../../../types/oauth';
import { resolveImplementation } from '../../../lib/utils/implementationResolver';
import v1Routes from '.';

jest.mock('../../../../src/lib/utils/implementationResolver');

describe('V1 Auth Routes', () => {
  let app: express.Application;
  const mockImplementation = {
    getAuthMiddleware: jest.fn(),
    getCallbackMiddleware: jest.fn(),
    refreshToken: jest.fn(),
  };
  const validPayload: AuthPayload = {
    accountDefinition: {
      type: 'test',
      category: 'destination',
      name: 'test-account',
      authenticationType: 'oauth',
    },
    account: {
      options: {
        region: 'us',
      },
      accountDefinitionName: 'test-account',
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    app = express();
    app.use(express.json());
    app.use('/auth/v1', v1Routes);

    (resolveImplementation as jest.Mock).mockReturnValue(mockImplementation);
  });

  describe('POST /auth/v1/url', () => {
    it('should handle successful auth middleware execution', async () => {
      mockImplementation.getAuthMiddleware.mockImplementation(() => (req: any, res: any) => {
        res.status(200).json({ success: true });
      });

      const response = await request(app).post('/auth/v1/url').send(validPayload);

      expect(response.status).toBe(200);
      expect(resolveImplementation).toHaveBeenCalledWith(validPayload.accountDefinition);
      expect(mockImplementation.getAuthMiddleware).toHaveBeenCalled();
    });

    it('should handle auth middleware error', async () => {
      mockImplementation.getAuthMiddleware.mockImplementation(() => {
        throw new Error('Auth middleware error');
      });

      const response = await request(app).post('/auth/v1/url').send(validPayload);

      expect(response.status).toBe(500);
    });

    it('should handle invalid payload', async () => {
      const response = await request(app).post('/auth/v1/url').send({});

      expect(response.status).toBe(500);
    });
  });

  describe('POST /auth/v1/callback', () => {
    it('should handle successful callback execution', async () => {
      const mockAccount = { id: '123', name: 'Test Account' };
      mockImplementation.getCallbackMiddleware.mockImplementation(() => (req: any, res: any) => {
        (req as any).account = mockAccount;
        res.status(200).json(mockAccount);
      });

      const response = await request(app).post('/auth/v1/callback').send(validPayload);

      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockAccount);
      expect(resolveImplementation).toHaveBeenCalledWith(validPayload.accountDefinition);
      expect(mockImplementation.getCallbackMiddleware).toHaveBeenCalled();
    });

    it('should handle callback middleware error', async () => {
      mockImplementation.getCallbackMiddleware.mockImplementation(() => {
        throw new Error('Callback middleware error');
      });

      const response = await request(app).post('/auth/v1/callback').send(validPayload);

      expect(response.status).toBe(500);
    });

    it('should handle invalid payload', async () => {
      const response = await request(app).post('/auth/v1/callback').send({});

      expect(response.status).toBe(500);
    });
  });

  describe('POST /auth/v1/refresh', () => {
    const validRefreshPayload: AuthPayload = {
      accountDefinition: {
        type: 'test',
        category: 'destination',
        name: 'test-account',
        authenticationType: 'oauth',
      },
      account: {
        accountDefinitionName: 'test-account',
        secret: {
          refreshToken: 'test-refresh-token',
        },
        options: {
          scope: ['read', 'write'],
        },
      },
    };

    it('should handle successful token refresh', async () => {
      const mockRefreshResult = {
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresIn: 3600,
      };

      mockImplementation.refreshToken.mockResolvedValue(mockRefreshResult);

      const response = await request(app).post('/auth/v1/refresh').send(validRefreshPayload);

      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockRefreshResult);
      expect(resolveImplementation).toHaveBeenCalledWith(validRefreshPayload.accountDefinition);
      expect(mockImplementation.refreshToken).toHaveBeenCalledWith(
        validRefreshPayload.account.secret?.refreshToken,
        validRefreshPayload.account.options,
      );
    });

    it('should handle refresh token error', async () => {
      mockImplementation.refreshToken.mockRejectedValue(new Error('Refresh token error'));

      const response = await request(app).post('/auth/v1/refresh').send(validRefreshPayload);

      expect(response.status).toBe(500);
    });

    it('should handle invalid payload', async () => {
      const response = await request(app).post('/auth/v1/refresh').send({});

      expect(response.status).toBe(500);
    });

    it('should handle missing refresh token', async () => {
      const invalidPayload = {
        ...validPayload,
        account: {
          secret: {},
          options: {},
        },
      };

      const response = await request(app).post('/auth/v1/refresh').send(invalidPayload);

      expect(response.status).toBe(500);
    });
  });
});
