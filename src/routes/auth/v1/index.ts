import express from 'express';
import { AuthPayload } from '../../../types/oauth';
import { resolveImplementation } from '../../../lib/utils/implementationResolver';

const router = express.Router();

// Auth route
router.post('/url', async (req, res, next) => {
  try {
    const payload: AuthPayload = req.body;
    const { accountDefinition } = payload;

    // Get implementation and use its middleware
    const implementation = resolveImplementation(accountDefinition);

    // Use the implementation's middleware
    implementation.getAuthMiddleware()(req, res, (err: any) => {
      if (err) return next(err);
      return next();
    });
  } catch (err) {
    next(err);
  }
});

// Generic callback route
router.post('/callback', async (req, res, next) => {
  try {
    const payload: AuthPayload = req.body;
    const { accountDefinition } = payload;
    const implementation = resolveImplementation(accountDefinition);

    // Use implementation's callback middleware
    implementation.getCallbackMiddleware()(req, res, (err: any) => {
      if (err) return next(err);
      res.send((req as any).account);
      return undefined;
    });
  } catch (err) {
    next(err);
  }
});

// Refresh token route
router.post('/refresh', async (req, res, next) => {
  try {
    const payload: AuthPayload = req.body;
    const { accountDefinition } = payload;

    const implementation = resolveImplementation(accountDefinition);

    const result = await implementation.refreshToken(
      req.body.account.secret.refreshToken,
      req.body.account.options,
    );

    res.json(result);
  } catch (err) {
    next(err);
  }
});

export default router;
