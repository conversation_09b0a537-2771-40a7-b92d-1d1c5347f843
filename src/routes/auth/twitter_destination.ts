import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';
import { RudderCategory } from '../../lib/constants';

const session = require('express-session');

// register passport strategy
const TwitterStrategy = require('passport-twitter').Strategy;

export default (authProps: any) => {
  const { role } = authProps;
  const router: express.Router = express.Router();
  type Twitter_config = {
    session_secret: string;
    secure_cookie: string;
  };

  const { session_secret, secure_cookie } = utils.getFromConfig<Twitter_config>(
    `oauth.credentials.${role}`,
  );
  router.use(
    session({
      resave: false,
      saveUninitialized: true,
      secret: session_secret,
      cookie: { secure: secure_cookie === 'true' },
    }),
  );
  Object.values(RudderCategory).forEach((rudderCategory) => {
    const strategyName = utils.getStrategyName(role, rudderCategory);
    const oAuthOptions = utils.oauthStrategyConsumerOptions(role, rudderCategory);
    passport.use(
      strategyName,
      new TwitterStrategy(
        oAuthOptions,
        (
          accessToken: string,
          accessTokenSecret: string,
          profile: any,
          callback: TSPassportCallback,
        ) => {
          utils.handleCallback(callback, {
            role,
            profile,
            secret: {
              accessToken,
              accessTokenSecret,
              consumerKey: oAuthOptions.consumerKey,
              consumerSecret: oAuthOptions.consumerSecret,
            },
          });
        },
      ),
    );
  });
  router.get(
    '/authz',
    (req: express.Request, res: express.Response, next: express.NextFunction) => {
      const strategyName = utils.getStrategyName(role, req.rudderCategory);
      return passport.authenticate(strategyName)(req, res, next);
    },
  );
  router.get(
    '/callback',
    (req: express.Request, res: express.Response, next: express.NextFunction) => {
      const strategyName = utils.getStrategyName(role, req.rudderCategory);
      return passport.authorize(strategyName)(req, res, next);
    },
    (req: any, res) => {
      res.send(req.account);
    },
  );
  return router;
};
