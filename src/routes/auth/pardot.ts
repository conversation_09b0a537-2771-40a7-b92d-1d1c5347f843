import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import { URL } from 'url';
import utils from '../../lib/utils/oAuthUtils';
import { RudderCategory, roles } from '../../lib/constants';

const router: express.Router = express.Router();
const role = roles.PARDOT;

// register passport strategy
const SalesforceStrategy = require('passport-salesforce').Strategy;

Object.values(RudderCategory).forEach((rudderCategory) => {
  const opts = utils.oauthStrategyOptions(role, rudderCategory);
  const strategyName = utils.getStrategyName(role, rudderCategory);
  passport.use(
    strategyName,
    new SalesforceStrategy(
      opts,
      (access_token: string, refresh_token: string, profile: any, callback: TSPassportCallback) => {
        const { hostname } = new URL(profile.urls.rest);
        const instance_url = `https://${hostname}`;
        utils.handleCallback(callback, {
          role,
          profile,
          options: {
            instance_url,
          },
          secret: { access_token, refresh_token, instance_url },
        });
      },
    ),
  );
});

const authzHandler = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  let scope = '';
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  if (req.rudderCategory === RudderCategory.Destination) {
    scope = 'offline_access pardot_api';
    return passport.authenticate(strategyName, { scope })(req, res, next);
  }
  return passport.authenticate(strategyName)(req, res, next);
};

const callbackHandler = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction,
) => {
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  return passport.authorize(strategyName)(req, res, next);
};

// setup authentication and callback routes
router.get('/authz', authzHandler);

router.get('/callback', callbackHandler, (req: any, res) => {
  res.send(req.account);
});

export default router;
