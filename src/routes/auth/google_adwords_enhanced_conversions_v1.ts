import { AuthSetupOptions } from 'auth';
import google_adwords from '../../controllers/oauthImplementations/google_adwords_v1';
import { RudderCategory, roles } from '../../lib/constants';

const authenticateOptions = new Map<RudderCategory, any>();
const role = roles.GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_V1;
authenticateOptions.set(RudderCategory.Destination, {
  accessType: 'offline',
  prompt: 'consent',
  scope: [
    'https://www.googleapis.com/auth/userinfo.profile',
    'https://www.googleapis.com/auth/userinfo.email',
    'https://www.googleapis.com/auth/adwords',
  ],
});

const authenticateOptsObj: AuthSetupOptions = {
  authenticateOptions,
  role,
};

export default google_adwords(authenticateOptsObj);
