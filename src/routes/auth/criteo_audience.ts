/* eslint-disable @typescript-eslint/no-useless-constructor */
import express from 'express';
import passport from 'passport';
import passportOauth2 from 'passport-oauth2';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';
import { RudderCategory, roles } from '../../lib/constants';

const state = 'togorot';
const router: express.Router = express.Router();
const OAuth2Strategy = passportOauth2.Strategy;
const role = roles.CRITEO_AUDIENCE;
class CriteoAudience extends OAuth2Strategy {
  constructor(
    options: passportOauth2.StrategyOptions,
    verifyFunction: passportOauth2.VerifyFunction,
  ) {
    super(options, verifyFunction);
  }
}

Object.values(RudderCategory).forEach((rudderCategory) => {
  const strategyName = utils.getStrategyName(role, rudderCategory);
  const opts = utils.oauthStrategyOptions(role, rudderCategory);
  passport.use(
    strategyName,
    new CriteoAudience(
      {
        ...opts,
        authorizationURL: 'https://consent.criteo.com/request',
        tokenURL: 'https://api.criteo.com/oauth2/token',
      },
      (accessToken: string, refreshToken: string, profile: any, callback: TSPassportCallback) => {
        utils.handleCallback(callback, {
          role,
          profile,
          secret: { accessToken, refreshToken },
        });
      },
    ),
  );
});

const authzHandler = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  if (req.rudderCategory === RudderCategory.Destination) {
    return passport.authenticate(strategyName, { state })(req, res, next);
  }
  return passport.authenticate(strategyName)(req, res, next);
};
const callbackHandler = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction,
) => {
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  return passport.authorize(strategyName)(req, res, next);
};
router.get('/authz', authzHandler);
router.get('/callback', callbackHandler, (req: any, res) => {
  res.send(req.account);
});
export default router;
