// import google_analytics from "../../controllers/oauthImplementations/google_analytics";
import { AuthSetupOptions } from 'auth';
import google_auth_incl_category from '../../controllers/oauthImplementations/google_auth_incl_category';
import { RudderCategory, roles } from '../../lib/constants';

const authenticateOptions = new Map<RudderCategory, any>();
const role = roles.GOOGLE_ANALYTICS_4;

authenticateOptions.set(RudderCategory.Destination, {
  accessType: 'offline',
  prompt: 'consent',
  scope: [
    'https://www.googleapis.com/auth/userinfo.profile',
    'https://www.googleapis.com/auth/userinfo.email',
    'https://www.googleapis.com/auth/analytics',
    'https://www.googleapis.com/auth/analytics.readonly',
  ],
});

const authenticateOptsObj: AuthSetupOptions = {
  authenticateOptions,
  role,
  supportLegacy: true,
};

// export default google_analytics("google_analytics");
export default google_auth_incl_category(authenticateOptsObj);
