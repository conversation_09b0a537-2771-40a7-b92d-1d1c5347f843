import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';
import { roles } from '../../lib/constants';

const role = roles.QUICKBOOKS;
const clientOptions = utils.oauthStrategyOptions(role);

const router: express.Router = express.Router();
const QuickbooksStrategy = require('../../lib/passport/quickbooks').Strategy;

const scope = ['com.intuit.quickbooks.accounting', 'openid', 'profile', 'email'];

passport.use(
  role,
  new QuickbooksStrategy(
    {
      authorizationURL: 'https://appcenter.intuit.com/connect/oauth2',
      tokenURL: 'https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer',
      state: 'security_token',
      passReqToCallback: true,
      ...clientOptions,
    },
    (
      req: any,
      accessToken: string,
      refreshToken: string,
      profile: any,
      callback: TSPassportCallback,
    ) => {
      const { realmId } = req.query;
      utils.handleCallback(callback, {
        role,
        profile,
        options: {
          realmId,
        },
        secret: { accessToken, refreshToken },
      });
    },
  ),
);

// setup authentication and callback routes
router.get('/authz', (req, res, d) => {
  // @ts-expect-error: session is not directly available on express.Request but seems to be necessary for this integration
  req.session = {};
  return passport.authenticate(role, {
    scope,
    state: 'security_token',
  })(req, res, d);
});

router.get(
  '/callback',
  (req, res, d) => {
    // @ts-expect-error: session is not directly available on express.Request but seems to be necessary for this integration
    req.session = {
      'oauth2:appcenter.intuit.com': { state: req.query.state },
    };
    return passport.authorize(role)(req, res, d);
  },
  (req: any, res) => {
    res.send(req.account);
  },
);

export default router;
