/* eslint-disable @typescript-eslint/no-useless-constructor */
import express from 'express';
import passport from 'passport';
import passportOauth2 from 'passport-oauth2';
import { TSPassportCallback } from 'auth';
import { RudderCategory, roles } from '../../lib/constants';
import utils from '../../lib/utils/oAuthUtils';

const scope = 'advertising::audiences';

const router: express.Router = express.Router();
const OAuth2Strategy = passportOauth2.Strategy;
const role = roles.AMAZON_AUDIENCE;

class AmazonAudience extends OAuth2Strategy {
  constructor(
    options: passportOauth2.StrategyOptions,
    verifyFunction: passportOauth2.VerifyFunction,
  ) {
    super(options, verifyFunction);
  }

  authenticate: any;
}

Object.values(RudderCategory).forEach((rudderCategory) => {
  const strategyName = utils.getStrategyName(role, rudderCategory);
  const opts = utils.oauthStrategyOptions(role, rudderCategory);
  passport.use(
    strategyName,
    new AmazonAudience(
      {
        ...opts,
        authorizationURL: 'https://www.amazon.com/ap/oa',
        tokenURL: 'https://api.amazon.com/auth/o2/token',
        scope,
      },
      (access_token: string, refresh_token: string, profile: any, callback: TSPassportCallback) => {
        utils.handleCallback(callback, {
          role,
          profile,
          secret: {
            accessToken: access_token,
            refreshToken: refresh_token,
            clientId: opts.clientID,
          },
        });
      },
    ),
  );
});

const authzHandler = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  if (req.rudderCategory === RudderCategory.Destination) {
    return passport.authenticate(strategyName, { scope })(req, res, next);
  }
  return passport.authenticate(strategyName)(req, res, next);
};
const callbackHandler = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction,
) => {
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  return passport.authorize(strategyName, { scope })(req, res, next);
};

router.get('/authz', authzHandler);
router.get('/callback', callbackHandler, (req: any, res) => {
  res.send(req.account);
});
export default router;
