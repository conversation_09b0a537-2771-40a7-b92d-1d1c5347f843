import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';
import { roles } from '../../lib/constants';

const router: express.Router = express.Router();
const role = roles.STRIPE;
/*
      example of expected data in profile, we cannot extract a meaningful name yet.
      {
        token_type: 'bearer',
        stripe_publishable_key: 'pk_live_abcdefghijklmn',
        scope: 'read_only',
        livemode: true,
        stripe_user_id: 'acct_abcdefghijklmn'
      }
    */
// register passport strategy
const StripeStrategy = require('passport-stripe').Strategy;

passport.use(
  role,
  new StripeStrategy(
    utils.oauthStrategyOptions(role),
    (accessToken: string, refreshToken: string, profile: any, callback: TSPassportCallback) => {
      utils.handleCallback(callback, {
        renderAccountInfo: (actProfile) => ({
          accountName: `Stripe - ${actProfile.stripe_user_id}`,
          metadata: { userId: actProfile.stripe_user_id },
        }),
        role,
        profile,
        options: {
          stripeUserId: profile.stripe_user_id,
        },
        secret: { accessToken, refreshToken },
      });
    },
  ),
);

// setup authentication and callback routes
router.get('/authz', passport.authenticate(role));

router.get('/callback', passport.authorize(role), (req: any, res) => {
  res.send(req.account);
});

export default router;
