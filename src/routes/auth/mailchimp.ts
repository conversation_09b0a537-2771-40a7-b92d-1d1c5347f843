import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';
import { roles } from '../../lib/constants';

const router: express.Router = express.Router();
const role = roles.MAILCHIMP;

// register passport strategy
const MailchimpStrategy = require('passport-mailchimp').Strategy;

passport.use(
  role,
  new MailchimpStrategy(
    utils.oauthStrategyOptions(role),
    (accessToken: string, refreshToken: string, profile: any, callback: TSPassportCallback) => {
      utils.handleCallback(callback, {
        role,
        profile,
        options: {
          apiEndpoint: profile.api_endpoint,
        },
        secret: { accessToken, refreshToken },
      });
    },
  ),
);

// setup authentication and callback routes
router.get('/authz', passport.authenticate(role));

router.get('/callback', passport.authorize(role), (req: any, res) => {
  res.send(req.account);
});

export default router;
