import express from 'express';
import directoryImporter from '../../lib/utils/directoryImporter';
import oAuthUtils from '../../lib/utils/oAuthUtils';
import v1Routes from './v1';

const oAuthRoutes = directoryImporter(__filename);
const router: express.Router = express.Router();

// V1 routes
router.use('/v1', v1Routes);

// Legacy routes
Object.keys(oAuthRoutes).forEach((role) => {
  router.use(`/${role}`, oAuthRoutes[role]);
  router.use(
    `/:rudderCategory/${role}`,
    oAuthUtils.validateRudderCategoryMiddleware,
    oAuthRoutes[role],
  );
});

export default router;
