import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';
import { roles } from '../../lib/constants';

const router: express.Router = express.Router();
const role = roles.GOOGLE_REVIEWS;

// register passport strategy
const GoogleStrategy = require('passport-google-oauth20').Strategy;

passport.use(
  role,
  new GoogleStrategy(
    utils.oauthStrategyOptions(role),
    (accessToken: string, refreshToken: string, profile: any, callback: TSPassportCallback) => {
      utils.handleCallback(callback, {
        role,
        profile,
        secret: { accessToken, refreshToken },
      });
    },
  ),
);

// setup authentication and callback routes
router.get(
  '/authz',
  // @ts-expect-error: passport object from `passport` is having this overload, es<PERSON> is not able to rightly point out the overload
  passport.authenticate(role, {
    accessType: 'offline',
    prompt: 'consent',
    scope: [
      'https://www.googleapis.com/auth/userinfo.profile',
      'https://www.googleapis.com/auth/userinfo.emai,',
      'https://www.googleapis.com/auth/plus.business.manage',
    ],
  }),
);

router.get('/callback', passport.authorize(role), (req: any, res) => {
  res.send(req.account);
});

export default router;
