import { isEmpty } from 'lodash';

export type RegionKeys = 'US' | 'AU' | 'EU' | 'IN' | 'CN' | 'JP' | 'CA';

export const DATA_CENTRE_BASE_ENDPOINTS_MAP: Record<RegionKeys, string> = {
  US: 'https://accounts.zoho.com',
  AU: 'https://accounts.zoho.com.au',
  EU: 'https://accounts.zoho.eu',
  IN: 'https://accounts.zoho.in',
  CN: 'https://accounts.zoho.com.cn',
  JP: 'https://accounts.zoho.jp',
  CA: 'https://accounts.zohocloud.ca',
};

export const getZohoRegion = (region?: string): RegionKeys => {
  if (region && typeof region !== 'string') {
    return '' as RegionKeys;
  }
  return !isEmpty(region) ? (region?.trim()?.toUpperCase() as RegionKeys) : 'US';
};

export const getZohoDC = (region?: string): string =>
  DATA_CENTRE_BASE_ENDPOINTS_MAP[getZohoRegion(region)];
