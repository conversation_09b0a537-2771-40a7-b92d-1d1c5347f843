import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';
import { RudderCategory, roles } from '../../lib/constants';

const router: express.Router = express.Router();
const role = roles.SNAPCHAT_CONVERSION;
// register passport strategy
const SnapchatStrategy = require('passport-snapchat').Strategy;

const profileFields = ['id', 'displayName'];
const scope = [
  'snapchat-marketing-api',
  'snapchat-offline-conversions-api',
  'snapchat-profile-api',
];

Object.values(RudderCategory).forEach((rudderCategory) => {
  const opts = utils.oauthStrategyOptions(role, rudderCategory);
  const strategyName = utils.getStrategyName(role, rudderCategory);
  passport.use(
    strategyName,
    new SnapchatStrategy(
      {
        ...opts,
        profileFields,
        scope,
      },
      (access_token: string, refresh_token: string, profile: any, callback: TSPassportCallback) => {
        utils.handleCallback(callback, {
          role,
          profile,
          secret: { access_token, refresh_token },
        });
      },
    ),
  );
});

const authzHandler = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  if (req.rudderCategory === RudderCategory.Destination) {
    return passport.authenticate(strategyName, {})(req, res, next);
  }
  return passport.authenticate(strategyName)(req, res, next);
};

const callbackHandler = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction,
) => {
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  return passport.authorize(strategyName)(req, res, next);
};

// setup authentication and callback routes
router.get('/authz', authzHandler);

router.get('/callback', callbackHandler, (req: any, res: any) => {
  res.send(req.account);
});

export default router;
