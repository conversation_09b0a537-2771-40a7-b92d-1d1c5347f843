import express from 'express';
import passport from 'passport';
import passportOauth2, { InternalOAuthError } from 'passport-oauth2';
import axios, { AxiosRequestConfig } from 'axios';
import { XMLParser } from 'fast-xml-parser';
import get from 'get-value';
import { TSPassportCallback } from 'auth';
import moment from 'moment';
import { RudderCategory } from '../../lib/constants';
import utils from '../../lib/utils/oAuthUtils';

const xmlParser = new XMLParser({ removeNSPrefix: true, parseAttributeValue: true });
const OAuth2Strategy = passportOauth2.Strategy;
type bingAdsProfile = {
  userName?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  name?: string;
};

export class BingAdsDestination extends OAuth2Strategy {
  developer_token: string;

  constructor(
    options: passportOauth2.StrategyOptions,
    verifyFunction: passportOauth2.VerifyFunction,
    developer_token: string,
  ) {
    const { ...restOptions } = options;
    super(restOptions, verifyFunction);
    this.developer_token = developer_token;
  }

  // eslint-disable-next-line class-methods-use-this
  userProfile(accessToken: string, done: (err?: Error | null, profile?: any) => void): void {
    // We have to call user related API
    const requestBody = `<s:Envelope xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">\n  <s:Header xmlns="https://bingads.microsoft.com/Customer/v13">\n    <Action mustUnderstand="1">GetUserInfo</Action>\n    <AuthenticationToken i:nil="false">${accessToken}</AuthenticationToken>\n    <DeveloperToken i:nil="false">${this.developer_token}</DeveloperToken>\n  </s:Header>\n  <s:Body>\n    <GetUserRequest xmlns="https://bingads.microsoft.com/Customer/v13">\n\n    </GetUserRequest>\n  </s:Body>\n</s:Envelope>`;
    const reqConfig: AxiosRequestConfig = {
      method: 'post',
      url: 'https://clientcenter.api.bingads.microsoft.com/Api/CustomerManagement/v13/CustomerManagementService.svc',
      headers: {
        'Content-Type': 'text/xml; charset=utf-8',
        SOAPAction: 'GetUser',
      },
      data: requestBody,
    };
    axios(reqConfig)
      .then((resp) => {
        const res = xmlParser.parse(resp?.data);
        const fault = get(res, 'Envelope.Body.Fault');
        const profile: bingAdsProfile = {};
        if (fault) {
          done(
            new InternalOAuthError(
              'Failed to fetch user profile. With error: ',
              JSON.stringify(fault),
            ),
          );
          return;
        }
        const user = get(res, 'Envelope.Body.GetUserResponse.User');
        const { FirstName, LastName, ContactInfo, UserName } = user;
        const name = [FirstName, LastName].filter(Boolean).join(' ').trim();
        if (name) profile.name = name;
        if (UserName) profile.userName = UserName;
        if (FirstName) profile.firstName = FirstName;
        if (LastName) profile.lastName = LastName;
        if (ContactInfo?.Email) profile.email = ContactInfo.Email;
        done(null, profile);
      })
      .catch((err: any) => {
        done(err);
      });
  }

  authenticate: any;
}

export default (authProps: any) => {
  const { scope, developer_token, role } = authProps;
  const router: express.Router = express.Router();
  Object.values(RudderCategory).forEach((rudderCategory) => {
    const strategyName = utils.getStrategyName(role, rudderCategory);
    const opts = utils.oauthStrategyOptions(role, rudderCategory);
    passport.use(
      strategyName,
      new BingAdsDestination(
        {
          ...opts,
          authorizationURL:
            'https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=select_account',
          tokenURL: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
          scope,
          skipUserProfile: false,
        },
        (
          access_token: string,
          refresh_token: string,
          response: any,
          profile: any,
          callback: TSPassportCallback,
        ) => {
          utils.handleCallback(callback, {
            role,
            profile,
            secret: {
              accessToken: access_token,
              refreshToken: refresh_token,
              developer_token,
              expirationDate: moment().add(response.expires_in, 's').toISOString(),
            },
          });
        },
        String(developer_token),
      ),
    );
  });

  router.get(
    '/authz',
    (req: express.Request, res: express.Response, next: express.NextFunction) => {
      const strategyName = utils.getStrategyName(role, req.rudderCategory);
      if (req.rudderCategory === RudderCategory.Destination) {
        return passport.authenticate(strategyName, { scope })(req, res, next);
      }
      return passport.authenticate(strategyName)(req, res, next);
    },
  );

  router.get(
    '/callback',
    (req: express.Request, res: express.Response, next: express.NextFunction) => {
      const strategyName = utils.getStrategyName(role, req.rudderCategory);
      return passport.authorize(strategyName, { scope })(req, res, next);
    },
    (req: any, res) => {
      res.send(req.account);
    },
  );
  return router;
};
