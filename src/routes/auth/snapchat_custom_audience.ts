import express from 'express';
import passport from 'passport';
import passportOauth2 from 'passport-oauth2';
import { TSPassportCallback } from 'auth';
import utils from '../../lib/utils/oAuthUtils';
import { RudderCategory, roles } from '../../lib/constants';

const scope = ['snapchat-marketing-api'];

const router: express.Router = express.Router();
const OAuth2Strategy = passportOauth2.Strategy;
const role = roles.SNAPCHAT_CUSTOM_AUDIENCE;
class SnapchatCustomAudience extends OAuth2Strategy {
  constructor(
    options: passportOauth2.StrategyOptions,
    verifyFunction: passportOauth2.VerifyFunction,
  ) {
    const { ...restOptions } = options;
    super(restOptions, verifyFunction);
  }

  authenticate: any;
}

Object.values(RudderCategory).forEach((rudderCategory) => {
  const strategyName = utils.getStrategyName(role, rudderCategory);
  const opts = utils.oauthStrategyOptions(role, rudderCategory);
  passport.use(
    strategyName,
    new SnapchatCustomAudience(
      {
        ...opts,
        authorizationURL: 'https://accounts.snapchat.com/login/oauth2/authorize',
        tokenURL: 'https://accounts.snapchat.com/accounts/oauth2/token',
      },
      (access_token: string, refresh_token: string, profile: any, callback: TSPassportCallback) => {
        utils.handleCallback(callback, {
          role,
          profile,
          secret: { access_token, refresh_token },
        });
      },
    ),
  );
});

const authzHandler = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  if (req.rudderCategory === RudderCategory.Destination) {
    return passport.authenticate(strategyName, { scope })(req, res, next);
  }
  return passport.authenticate(strategyName)(req, res, next);
};
const callbackHandler = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction,
) => {
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  return passport.authorize(strategyName)(req, res, next);
};
router.get('/authz', authzHandler);
router.get('/callback', callbackHandler, (req: any, res) => {
  res.send(req.account);
});
export default router;
