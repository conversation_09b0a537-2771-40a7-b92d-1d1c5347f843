import express from 'express';
import passport from 'passport';
import { TSPassportCallback } from 'auth';
import url from 'url';
import axios from 'axios';
import qs = require('qs');
import { RudderCategory, roles } from '../../lib/constants';
import utils from '../../lib/utils/oAuthUtils';

const state = 'togorot';
const router: express.Router = express.Router();
const role = roles.REDDIT;

const RedditStrategy = require('../../lib/passport/reddit').Strategy;

const scope = ['adsread', 'adsconversions', 'history'];

let opts: any;

Object.values(RudderCategory).forEach((rudderCategory) => {
  const strategyName = utils.getStrategyName(role, rudderCategory);
  opts = utils.oauthStrategyOptions(role, rudderCategory);
  passport.use(
    strategyName,
    new RedditStrategy(
      {
        ...opts,
        authorizationURL: 'https://www.reddit.com/api/v1/authorize',
        tokenURL: 'https://www.reddit.com/api/v1/access_token',
        scope,
      },
      (accessToken: string, refreshToken: string, profile: any, callback: TSPassportCallback) => {
        utils.handleCallback(callback, {
          role,
          profile,
          secret: { accessToken, refreshToken },
        });
      },
    ),
  );
});

export const authzHandler = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction,
) => {
  const strategyName = utils.getStrategyName(role, req.rudderCategory);
  if (req.rudderCategory === RudderCategory.Destination) {
    return passport.authenticate(strategyName, { state })(req, res, next);
  }
  return passport.authenticate(strategyName)(req, res, next);
};
export const callbackHandler = async (req: express.Request, res: express.Response) => {
  opts = utils.oauthStrategyOptions(role, req.rudderCategory);
  const credentialsString = `${opts.clientID}:${opts.clientSecret}`;
  const encodedCredentials = Buffer.from(credentialsString).toString('base64');
  const urlObject = url.parse(req.url, true);
  const data = qs.stringify({
    grant_type: 'authorization_code',
    redirect_uri: opts.callbackURL,
    code: urlObject.query.code,
  });
  try {
    const response = await axios.request({
      method: 'POST',
      url: 'https://www.reddit.com/api/v1/access_token',
      headers: {
        'content-type': 'application/x-www-form-urlencoded',
        Authorization: `Basic ${encodedCredentials}`,
      },
      data,
    });
    const responseData = response.data;
    res.send({
      secret: {
        accessToken: Object(responseData)?.access_token,
        refreshToken: Object(responseData)?.refresh_token,
      },
    });
  } catch (err) {
    res.status(400).send({ success: false, err });
  }
};

router.get('/authz', authzHandler);
router.get('/callback', callbackHandler, (req: any, res) => {
  res.send(req.account);
});
export default router;
