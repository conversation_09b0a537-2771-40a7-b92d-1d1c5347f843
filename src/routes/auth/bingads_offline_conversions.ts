import config from 'config';
import { roles } from '../../lib/constants';
import bing_ads_destination from './bing_ads_destination';

const role = roles.BINGADS_OFFLINE_CONVERSIONS;
const developer_token = config.get(`oauth.credentials.${role}.developer_token`);
export default bing_ads_destination({
  scope: ['profile email openid offline_access https://ads.microsoft.com/msads.manage'],
  role,
  developer_token,
});
