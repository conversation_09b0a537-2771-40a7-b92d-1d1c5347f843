import { AuthSetupOptions } from 'auth';
import tiktok from '../../controllers/oauthImplementations/tiktok';
import { RudderCategory, roles } from '../../lib/constants';

const authenticateOptions = new Map<RudderCategory, any>();
const role = roles.TIKTOK_AUDIENCE;
authenticateOptions.set(RudderCategory.Destination, {});

const authenticateOptsObj: AuthSetupOptions = {
  authenticateOptions,
  role,
};

export default tiktok(authenticateOptsObj);
