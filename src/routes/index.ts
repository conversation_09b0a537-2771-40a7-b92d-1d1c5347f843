import express from 'express';
import auth from './auth';
import tokens from './tokens';

// There is a header "x-rudder-category" which actually contains info about the rudder's category(source|destination)
// If the value is 'source' or ''(backward compatibility), then the request is about a source/managing source OAuth
// If the value is 'destination', then the request is about a destination/managing destination OAuth
const router: express.Router = express.Router();

router.use('/auth', auth);
router.use('/tokens', tokens);

export default router;
