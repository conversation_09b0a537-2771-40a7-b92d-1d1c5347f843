import express from 'express';
import { NotFoundError } from '../lib/error';
import oAuthUtils from '../lib/utils/oAuthUtils';
import refreshTokenMethods from '../controllers/refreshTokenMethods';

const router: express.Router = express.Router();
// Backward compatibility
router.post(
  '/:role/refresh',
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { role } = req.params;
      const refreshMethod = refreshTokenMethods[role];
      if (!refreshMethod) {
        throw new NotFoundError('[refresh] role not found', 'role_not_found');
      }
      const newCredentials = await refreshMethod(req.body);
      res.status(200).send(newCredentials);
    } catch (err) {
      next(err);
    }
  },
);

router.post(
  '/:rudderCategory/:role/refresh',
  oAuthUtils.validateRudderCategoryMiddleware,
  async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      const { role } = req.params;
      const refreshMethod = refreshTokenMethods[role];
      const { flow, config } = req;

      if (!refreshMethod) {
        throw new NotFoundError('[refresh] role not found', 'role_not_found');
      }
      const newCredentials = await refreshMethod(req.body, req.rudderCategory, {
        ...(flow && { flow }),
        ...(config && { config }),
      });
      res.status(200).send(newCredentials);
    } catch (err) {
      next(err);
    }
  },
);

export default router;
