import client from 'prom-client';
import { Response, Request } from 'express';

const registry = new client.Registry();
const defaultLabels = {
  service: 'rudder-auth',
  instanceID: process.env.INSTANCE_ID || 'localhost',
};

// Define metrics here
export const metrics = {
  httpRequestDuration: new client.Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'path', 'status'] as const,
    buckets: [0.1, 0.5, 1, 2, 5, 10],
  }),
};

Object.values(metrics).forEach((metric) => registry.registerMetric(metric));

client.Registry.merge([registry]);
client.register.setDefaultLabels(defaultLabels);
client.collectDefaultMetrics();

export const metricsRouteHandler = async (_req: Request, res: Response) => {
  res.set('Content-Type', client.register.contentType);
  res.send(await client.register.metrics());
};
