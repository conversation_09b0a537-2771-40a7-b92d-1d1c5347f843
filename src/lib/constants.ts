// This constant is used to identify if a refresh token used for refreshing the token is invalid
export const REFRESH_TOKEN_INVALID_GRANT = 'refresh_token_invalid_grant';
// This constant is used to identify if a refresh token used for refreshing the token is invalid
// reason: updating the name to not be backward compatible
export const REF_TOKEN_INVALID_GRANT = 'ref_token_invalid_grant';

export enum RudderCategory {
  Destination = 'destination',
  Source = 'source',
}

// Please add role strings here, so that we maintain everything at a single-place
export const roles = {
  BING_ADS: 'bing_ads',
  BIGQUERY: 'bigquery',
  BINGADS_AUDIENCE: 'bingads_audience',
  BINGADS_OFFLINE_CONVERSIONS: 'bingads_offline_conversions',
  CRITEO_AUDIENCE: 'criteo_audience',
  CAMPAIGN_MANAGER: 'campaign_manager',
  FACEBOOK_ADS: 'facebook_ads',
  GOOGLE_ADWORDS: 'google_adwords',
  GOOGLE_SHEETS: 'google_sheets',
  GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_V1: 'google_adwords_enhanced_conversions_v1',
  GOOGLE_ADWORDS_ENHANCED_CONVERSIONS: 'google_adwords_enhanced_conversions',
  GOOGLE_ADWORDS_REMARKETING_LISTS_V1: 'google_adwords_remarketing_lists_v1',
  GOOGLE_ADWORDS_REMARKETING_LISTS: 'google_adwords_remarketing_lists',
  GOOGLE_ADWORDS_OFFLINE_CONVERSIONS: 'google_adwords_offline_conversions',
  GOOGLE_ANALYTICS: 'google_analytics',
  GOOGLE_ANALYTICS_4: 'google_analytics_4',
  GOOGLE_DFP: 'google_dfp',
  GOOGLE_REVIEWS: 'google_reviews',
  GOOGLE_SEARCH_CONSOLE: 'google_search_console',
  LINKEDIN_ADS: 'linkedin_ads',
  LINKEDIN_AUDIENCE: 'linkedin_audience',
  MAILCHIMP: 'mailchimp',
  INTERCOM: 'intercom',
  PARDOT: 'pardot',
  STRIPE: 'stripe',
  SNAPCHAT_CONVERSION: 'snapchat_conversion',
  SNAPCHAT_CUSTOM_AUDIENCE: 'snapchat_custom_audience',
  SINGER_GOOGLE_SHEETS: 'singer-google-sheets',
  SINGER_BING_ADS: 'singer-bing-ads',
  SINGER_FACEBOOK_MARKETING: 'singer-facebook-marketing',
  SINGER_GOOGLE_ADWORDS: 'singer-google_adwords',
  SINGER_GOOGLE_ANALYTICS: 'singer-google-analytics',
  SINGER_GOOGLE_SEARCH_CONSOLE: 'singer-google-search-console',
  SINGER_INTERCOM: 'singer-intercom',
  SINGER_PIPEDRIVE: 'singer-pipedrive',
  SINGER_SALESFORCE: 'singer-salesforce',
  SINGER_SALESFORCE_SANDBOX: 'singer-salesforce-sandbox',
  SINGER_ZENDESK_CHAT: 'singer-zendesk-chat',
  SINGER_ZENDESK_SUPPORT: 'singer-zendesk-support',
  TWITTER_ADS: 'twitter_ads',
  X_AUDIENCE: 'x_audience',
  TIKTOK_AUDIENCE: 'tiktok_audience',
  HUBSPOT: 'hubspot',
  HUBSPOT_CRM: 'hubspot_crm',
  QUICKBOOKS: 'quickbooks',
  SALESFORCE: 'salesforce',
  SALESFORCE_SANDBOX: 'salesforce_sandbox',
  ZENDESK: 'zendesk',
  ZENDESK_CHAT: 'zendesk_chat',
  XERO: 'xero',
  REDDIT: 'reddit',
  YANDEX_METRICA_OFFLINE_EVENTS: 'yandex_metrica_offline_events',
  ZOHO: 'zoho',
  AMAZON_AUDIENCE: 'amazon_audience',
};
