/*
 * This method returns a refreshed access token according to Auth0 standards
 */

import axios from 'axios';
import qs from 'querystring';
import moment from 'moment';
import { clientCredsConfig } from 'client-configs';
import oAuthUtils from './oAuthUtils';

const standardRefreshToken = async (options: {
  role: string;
  refreshToken: string;
  tokenURI: string;
}) => {
  const { role, refreshToken, tokenURI } = options;
  const { client_id, client_secret } = oAuthUtils.getFromConfig<clientCredsConfig>(
    `oauth.credentials.${role}`,
  );

  const response = await axios.post(
    tokenURI,
    qs.stringify({
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
    }),
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        authorization: `Basic ${Buffer.from(`${client_id}:${client_secret}`).toString('base64')}`,
      },
    },
  );
  const { access_token, refresh_token, expires_in } = response.data;
  return {
    accessToken: access_token,
    refreshToken: refresh_token,
    expirationDate: moment().add(expires_in, 's').toISOString(),
  };
};

export default standardRefreshToken;
