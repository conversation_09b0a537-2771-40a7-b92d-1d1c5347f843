import oAuthUtils from './oAuthUtils';

describe('oAuthUtils tests', () => {
  describe('getOAuthImplementationIdentifier tests', () => {
    it('should return a string with category, type, and authenticationType joined by underscores', () => {
      const accountDefinition = {
        name: 'Test Account',
        category: 'social',
        type: 'personal',
        authenticationType: 'oauth2',
      };

      const result = oAuthUtils.getOAuthImplementationIdentifier(accountDefinition);

      expect(result).toBe('social_personal_oauth2');
    });

    it('should throw an error when accountDefinition.category is undefined', () => {
      const accountDefinition = {
        name: 'Test Account',
        category: undefined,
        type: 'personal',
        authenticationType: 'oauth2',
      } as any;

      expect(() => {
        oAuthUtils.getOAuthImplementationIdentifier(accountDefinition);
      }).toThrow('Account definition must include category, type and authenticationType');
    });
  });

  describe('handleCallbackV1 tests', () => {
    it('should call accountInfo with profile and return callback with metadata and secret', () => {
      const profile = { userId: '123', displayName: 'Test User', email: '<EMAIL>' };
      const secret = 'test-secret';
      const callbackMock = jest.fn();

      oAuthUtils.handleCallbackV1(callbackMock, { profile, secret });

      const accountInfoData = oAuthUtils.accountInfo(profile);
      expect(callbackMock).toHaveBeenCalledWith(null, {
        metadata: accountInfoData.metadata,
        secret,
      });
    });
  });
});
