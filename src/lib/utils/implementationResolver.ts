import { AccountDefinition, OAuthImplementation } from '../../types/oauth';
import { IMPLEMENTATION_MAP } from '../config';
import oAuthUtils from './oAuthUtils';

// cache (exported only for testing not meant to be used otherwise)
export const implementationsCache: Map<string, OAuthImplementation> = new Map();

export function resolveImplementation(accountDefinition: AccountDefinition): OAuthImplementation {
  const implementationKey = oAuthUtils.getOAuthImplementationIdentifier(accountDefinition);

  if (implementationsCache.has(implementationKey)) {
    return implementationsCache.get(implementationKey)!;
  }

  const config = IMPLEMENTATION_MAP[implementationKey.toLowerCase()];
  if (!config) {
    throw new Error(`No implementation found for type: ${implementationKey}`);
  }

  try {
    const { BaseImplementation, scope, options } = config;
    const implementation = new BaseImplementation(accountDefinition, scope, options);
    implementationsCache.set(implementationKey, implementation);
    return implementation;
  } catch (error: any) {
    throw new Error(`Failed to initialize implementation: ${error.message}`);
  }
}
