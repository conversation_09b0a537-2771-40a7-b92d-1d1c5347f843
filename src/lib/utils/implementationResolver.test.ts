import { resolveImplementation, implementationsCache } from './implementationResolver';
import { AccountDefinition, OAuthImplementation } from '../../types/oauth';
import { IMPLEMENTATION_MAP } from '../config';
import oAuthUtils from './oAuthUtils';

// Mock dependencies
jest.mock('../../../src/lib/config', () => ({
  IMPLEMENTATION_MAP: {
    test_implementation: {
      BaseImplementation: jest.fn(),
      scope: ['test_scope'],
      options: { testOption: 'value' },
    },
  },
}));

jest.mock('../../../src/lib/utils/oAuthUtils', () => ({
  getOAuthImplementationIdentifier: jest.fn(),
}));

describe('implementationResolver', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    // Clear the implementation cache
    implementationsCache.clear();
  });

  it('should resolve implementation successfully', () => {
    // Arrange
    const accountDefinition: AccountDefinition = {
      name: 'test_name',
      type: 'test_type',
      category: 'test_category',
      authenticationType: 'test_auth_type',
    };
    const mockImplementation = {} as OAuthImplementation;

    (oAuthUtils.getOAuthImplementationIdentifier as jest.Mock).mockReturnValue(
      'test_implementation',
    );
    (IMPLEMENTATION_MAP.test_implementation.BaseImplementation as jest.Mock).mockImplementation(
      () => mockImplementation,
    );

    // Act
    const result = resolveImplementation(accountDefinition);

    // Assert
    expect(result).toBe(mockImplementation);
    expect(oAuthUtils.getOAuthImplementationIdentifier).toHaveBeenCalledWith(accountDefinition);
    expect(IMPLEMENTATION_MAP.test_implementation.BaseImplementation).toHaveBeenCalledWith(
      accountDefinition,
      ['test_scope'],
      { testOption: 'value' },
    );
  });

  it('should return cached implementation if available', () => {
    // Arrange
    const accountDefinition: AccountDefinition = {
      name: 'test_name',
      type: 'test_type',
      category: 'test_category',
      authenticationType: 'test_auth_type',
    };
    const mockImplementation = {} as OAuthImplementation;

    (oAuthUtils.getOAuthImplementationIdentifier as jest.Mock).mockReturnValue(
      'test_implementation',
    );
    (IMPLEMENTATION_MAP.test_implementation.BaseImplementation as jest.Mock).mockImplementation(
      () => mockImplementation,
    );

    // Act
    const firstResult = resolveImplementation(accountDefinition);
    const secondResult = resolveImplementation(accountDefinition);

    // Assert
    expect(firstResult).toBe(secondResult);
    expect(IMPLEMENTATION_MAP.test_implementation.BaseImplementation).toHaveBeenCalledTimes(1);
  });

  it('should throw error when implementation not found', () => {
    // Arrange
    const accountDefinition: AccountDefinition = {
      name: 'test_name',
      type: 'unknown_type',
      category: 'test_category',
      authenticationType: 'test_auth_type',
    };

    (oAuthUtils.getOAuthImplementationIdentifier as jest.Mock).mockReturnValue(
      'unknown_implementation',
    );

    // Act & Assert
    expect(() => resolveImplementation(accountDefinition)).toThrow(
      'No implementation found for type: unknown_implementation',
    );
  });

  it('should throw error when implementation initialization fails', () => {
    // Arrange
    const accountDefinition: AccountDefinition = {
      name: 'test_name',
      type: 'test_type',
      category: 'test_category',
      authenticationType: 'test_auth_type',
    };
    const errorMessage = 'Initialization failed';

    (oAuthUtils.getOAuthImplementationIdentifier as jest.Mock).mockReturnValue(
      'test_implementation',
    );
    (IMPLEMENTATION_MAP.test_implementation.BaseImplementation as jest.Mock).mockImplementation(
      () => {
        throw new Error(errorMessage);
      },
    );

    // Act & Assert
    expect(() => resolveImplementation(accountDefinition)).toThrow(
      `Failed to initialize implementation: ${errorMessage}`,
    );
  });
});
