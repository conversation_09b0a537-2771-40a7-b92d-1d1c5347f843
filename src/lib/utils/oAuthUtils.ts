import config from 'config';
import { TSOAuthStrategyOptions, TSOAuthStrategyConsumerOptions } from 'auth';
import { NextFunction, Request, Response } from 'express';
import { AxiosError } from 'axios';
import { AccountDefinition, AccountMetadata } from '../../types/oauth';
import { RudderCategory, roles } from '../constants';
import { InvalidGrantError } from '../../types/error';

const rudderCategories = Object.values(RudderCategory);

const callbackURLByRole = (role: string): string => {
  if (config.has(`oauth.credentials.${role}.callback_url`)) {
    return config.get(`oauth.credentials.${role}.callback_url`);
  }
  return `${config.get('oauth.base_redirect_url')}/auth/${role}/callback`;
};

const getEnvironmentVariableKey = (role: string, rudderCategory?: string): string => {
  let envKey = `oauth.credentials.${role}`;
  if (rudderCategory) {
    envKey += `.${rudderCategory}`;
  }
  return envKey;
};

/**
 * Converts object to Form Data
 * @param {Object} obj
 * @returns {string}
 */
const toFormData = (obj: any) => {
  let formdata = '';
  // eslint-disable-next-line guard-for-in, no-restricted-syntax
  for (const key in obj) {
    if (typeof obj[key] === 'object') {
      formdata += `${key}=${JSON.stringify(obj[key])}&`;
    } else {
      formdata += `${key}=${obj[key]}&`;
    }
  }
  return formdata.slice(0, -1);
};

const extractAccountDetails = (profile: any = {}) => {
  const userId = profile.user_id || profile.id || profile.userId || profile.user_id;
  const email =
    profile.email ||
    (profile.emails && profile.emails[0] && profile.emails[0].value) ||
    profile.user ||
    (profile.login && profile.login.email) ||
    profile.preferred_username ||
    profile.profileURL;
  const displayName =
    profile.displayName ||
    profile.accountName ||
    profile.name ||
    ((profile.givenName || profile.given_name) &&
      (profile.familyName || profile.family_name) &&
      `${profile.givenName || profile.given_name} ${profile.familyName || profile.family_name}`);
  return {
    userId,
    email,
    displayName,
    userName: email || displayName || userId,
  };
};

const validateRudderCategoryMiddleware = (req: Request, res: Response, next: NextFunction) => {
  if (
    !req.params.rudderCategory ||
    !rudderCategories.includes(req.params.rudderCategory as RudderCategory)
  ) {
    res.status(404).send('The requested url not found');
    return;
  }
  req.newRoute = true;
  req.rudderCategory = req.params.rudderCategory as RudderCategory;
  next();
};

function getFromConfig<T>(key: string): T {
  return config.get(key);
}

const handleInvalidGrantError = (role: string, error: any) => {
  switch (role) {
    case roles.CRITEO_AUDIENCE:
      if ((error as AxiosError).response?.status === 403) {
        throw new InvalidGrantError(
          `[${role}] "invalid_grant" error, refresh token has expired or revoked`,
          403,
          'Forbidden',
        );
      }
      break;
    case roles.CAMPAIGN_MANAGER:
    case roles.GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_V1:
    case roles.GOOGLE_ADWORDS_ENHANCED_CONVERSIONS:
    case roles.GOOGLE_ADWORDS_OFFLINE_CONVERSIONS:
    case roles.GOOGLE_ADWORDS_REMARKETING_LISTS_V1:
    case roles.GOOGLE_ADWORDS_REMARKETING_LISTS:
    case roles.GOOGLE_ANALYTICS:
    case roles.GOOGLE_ANALYTICS_4:
    case roles.BINGADS_AUDIENCE:
    case roles.BINGADS_OFFLINE_CONVERSIONS:
    case roles.PARDOT:
    case roles.SALESFORCE:
    case roles.LINKEDIN_ADS:
    case roles.LINKEDIN_AUDIENCE:
    case roles.AMAZON_AUDIENCE:
      if ((error as AxiosError<any>).response?.data?.error === 'invalid_grant') {
        throw new InvalidGrantError(
          `[${role}] "invalid_grant" error, refresh token has expired or revoked`,
          403,
          'Forbidden',
        );
      }
      break;
    case roles.ZOHO: {
      const errStr =
        typeof error === 'string' ? error : (error as AxiosError<any>).response?.data?.error;
      if (['invalid_grant', 'invalid_code'].includes(errStr)) {
        throw new InvalidGrantError(
          `[${role}] "${errStr}" error, refresh token has expired or revoked. Please try reconfiguring your account in the destination setup.`,
          403,
          'Forbidden',
        );
      }
      break;
    }
    default:
      // No-op
      break;
  }
  throw error;
};

export const callbackUrlByRoleAndCategory = (
  role: string,
  rudderCategory?: RudderCategory,
): string => {
  if (!rudderCategory) {
    return callbackURLByRole(role);
  }
  const configKey = getEnvironmentVariableKey(role, rudderCategory);
  if (config.has(`${configKey}.callback_url`)) {
    return config.get(`${configKey}.callback_url`);
  }
  return `${config.get('oauth.base_redirect_url')}/auth/${rudderCategory}/${role}/callback`;
};

const getStaticCallBackUrl = (): string => `${config.get('oauth.base_redirect_url')}/oauth`;

export const accountInfo = (profile: any = {}) => {
  const { userId, displayName, email, userName } = extractAccountDetails(profile);
  const accountData = { userId, displayName, email, userName };
  // eslint-disable-next-line no-underscore-dangle
  if (profile._json) {
    const {
      userId: dUserID,
      displayName: dDisplayName,
      email: dEmail,
      // eslint-disable-next-line no-underscore-dangle
    } = extractAccountDetails(profile._json);
    accountData.userId = accountData.userId || dUserID;
    accountData.displayName = accountData.displayName || dDisplayName;
    accountData.email = accountData.email || dEmail;
    accountData.userName = accountData.userName || dEmail || dDisplayName || dUserID;
  }
  return {
    metadata: accountData as AccountMetadata,
    accountName: accountData.displayName,
  };
};

const oauthStrategyOptions = (
  role: string,
  rudderCategory: string = '',
): TSOAuthStrategyOptions => {
  const envKey = getEnvironmentVariableKey(role, rudderCategory);
  const opts: TSOAuthStrategyOptions = {
    clientID: config.get(`${envKey}.client_id`),
    clientSecret: config.get(`${envKey}.client_secret`),
    callbackURL: callbackUrlByRoleAndCategory(role, rudderCategory as RudderCategory),
  };
  return opts;
};

const getOAuthStrategyOptionsWithStaticCallback = (
  role: string,
  rudderCategory: string = '',
): TSOAuthStrategyOptions => {
  const envKey = getEnvironmentVariableKey(role, rudderCategory);
  return {
    clientID: config.get(`${envKey}.client_id`),
    clientSecret: config.get(`${envKey}.client_secret`),
    callbackURL: getStaticCallBackUrl(),
  };
};

const handleCallback = (
  callback: any,
  {
    renderAccountInfo,
    role,
    options = {},
    secret,
    profile,
  }: {
    renderAccountInfo?: (profile: any) => {
      accountName: string;
      metadata: AccountMetadata;
    };
    role: string;
    options?: any;
    secret: any;
    profile: any;
  },
) => {
  const accountInfoData = renderAccountInfo ? renderAccountInfo(profile) : accountInfo(profile);
  return callback(null, {
    role,
    name: accountInfoData.accountName,
    metadata: accountInfoData.metadata,
    options,
    secret,
    ...options,
    ...secret,
  });
};

const handleCallbackV1 = (callback: any, { profile, secret }: any) => {
  const accountInfoData = accountInfo(profile);
  return callback(null, {
    metadata: accountInfoData.metadata,
    secret,
  });
};

const oauthStrategyConsumerOptions = (
  role: string,
  rudderCategory: string = '',
): TSOAuthStrategyConsumerOptions => {
  const envKey = getEnvironmentVariableKey(role, rudderCategory);
  return {
    consumerKey: config.get(`${envKey}.consumer_key`),
    consumerSecret: config.get(`${envKey}.consumer_secret`),
    callbackURL: callbackUrlByRoleAndCategory(role, rudderCategory as RudderCategory),
    skipExtendedUserProfile: config.get(`${envKey}.skip_extended_user_profile`),
  };
};

const getStrategyName = (role: string, rudderCategory?: string | RudderCategory): string => {
  if (rudderCategory === RudderCategory.Destination) {
    return `${role}_${rudderCategory}`;
  }
  return role;
};

/**
 * Creates a unique identifier for OAuth implementations by combining account properties
 * @param accountDefinition The account definition containing category, type and authenticationType
 * @returns A string identifier in the format "category_type_authenticationType"
 * @throws {Error} If any required property is missing
 */
const getOAuthImplementationIdentifier = (accountDefinition: AccountDefinition): string => {
  if (
    !accountDefinition.category ||
    !accountDefinition.type ||
    !accountDefinition.authenticationType
  ) {
    throw new Error('Account definition must include category, type and authenticationType');
  }

  return [
    accountDefinition.category,
    accountDefinition.type,
    accountDefinition.authenticationType,
  ].join('_');
};

export default {
  accountInfo,
  callbackURLByRole,
  callbackUrlByRoleAndCategory,
  getEnvironmentVariableKey,
  getFromConfig,
  getOAuthImplementationIdentifier,
  getOAuthStrategyOptionsWithStaticCallback,
  getStrategyName,
  handleCallback,
  handleCallbackV1,
  handleInvalidGrantError,
  oauthStrategyConsumerOptions,
  oauthStrategyOptions,
  toFormData,
  validateRudderCategoryMiddleware,
};
