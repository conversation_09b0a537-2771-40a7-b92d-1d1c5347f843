/* eslint-disable import/no-dynamic-require */

/*
 * import all the files from a directory using the file name
 * as exported name if has default export otherwise use the method name
 */

import fs from 'fs';
import path from 'path';

export default (filePath: string): { [i: string]: any } => {
  const basename: string = path.basename(filePath);
  const folderToImport: string = path.dirname(filePath);

  interface ImportedFiles {
    [index: string]: string;
  }

  const importedFiles: ImportedFiles = {};

  fs.readdirSync(folderToImport)
    .filter(
      (file: string) =>
        file.indexOf('.') !== 0 &&
        file !== basename &&
        (file.slice(-3) === '.js' || file.slice(-3) === '.ts'),
    )
    .forEach((file: string) => {
      const file_path = path.join(folderToImport, file);
      const currentImportedFile: ImportedFiles = require(file_path);
      Object.keys(currentImportedFile).forEach((exportedName: string) => {
        const [name] = file.split('.');
        importedFiles[exportedName === 'default' ? name : exportedName] =
          currentImportedFile[exportedName];
      });
    });

  return importedFiles;
};
