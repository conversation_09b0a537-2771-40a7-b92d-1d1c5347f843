import express from 'express';
import { isAxiosError } from 'axios';
import logger from '../logger';

type ErrorResponse = {
  headers: any;
  data: any;
  status: any;
  statusText: any;
};
class LoggableError extends Error {
  config: any;

  response: ErrorResponse;

  constructor(message: string, config: any, response: ErrorResponse) {
    super(message);
    this.config = config;
    this.response = response;
  }
}

export default (
  err: any,
  req: express.Request,
  res: express.Response,
  next: express.NextFunction,
) => {
  if (!err) {
    return next();
  }
  if (isAxiosError(err)) {
    const loggableError = new LoggableError(err.message, err.config, {
      headers: err.response?.headers,
      data: err.response?.data,
      status: err?.response?.status,
      statusText: err?.response?.statusText,
    });
    loggableError.stack = err.stack;
    logger.error(loggableError);
  } else {
    logger.error(err);
  }
  const status = err.http_status_code || err.status || err.response?.status || 500;
  const errorData = err?.response?.data;

  const errorMessage = typeof errorData === 'string' ? errorData : errorData?.error?.message;
  const dataError = errorData?.error;

  const errorResponse = {
    message: errorMessage || err.message,
    status,
    code: dataError?.name || err.code || 'internalError',
  };

  res.status(status).send(errorResponse);
  return next();
};
