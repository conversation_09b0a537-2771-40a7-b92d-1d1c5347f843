/* eslint-disable import/prefer-default-export, no-param-reassign, @typescript-eslint/no-unused-expressions, no-sequences */
import Axios from 'axios';

const OAuth2Strategy = require('passport-oauth2').Strategy;
const util = require('util');

function Strategy(options: any, verify: any) {
  options = options || {};
  (options.authorizationURL =
    options.authorizationURL || 'https://appcenter.intuit.com/connect/oauth2'),
    (options.tokenURL =
      options.tokenURL || 'https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer');
  // @ts-expect-error: this seems to be of type any which is kind of not identified by linter
  OAuth2Strategy.call(this, options, verify);
}

util.inherits(Strategy, OAuth2Strategy);

Strategy.prototype.userProfile = function (accessToken: string, done: any) {
  Axios.get('https://accounts.platform.intuit.com/v1/openid_connect/userinfo', {
    headers: {
      Authorization: `Bear<PERSON> ${accessToken}`,
      Accept: 'application/json',
    },
  })
    .then((res) => {
      done(null, res.data);
    })
    .catch((err) => {
      console.log(err.response.data);
      done(null, {});
    });
};

export { Strategy };
