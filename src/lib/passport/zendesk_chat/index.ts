/* eslint-disable import/prefer-default-export */
/* eslint-disable no-param-reassign */
const OAuth2Strategy = require('passport-oauth2').Strategy;
const util = require('util');

function Strategy(options: any, verify: any) {
  options = options || {};
  options.authorizationURL =
    options.authorizationURL || 'https://www.zopim.com/oauth2/authorizations/new';
  options.tokenURL = options.tokenURL || 'https://www.zopim.com/oauth2/token';
  // @ts-expect-error: this seems to be of type any which is kind of not identified by linter
  OAuth2Strategy.call(this, options, verify);
}

util.inherits(Strategy, OAuth2Strategy);

Strategy.prototype.authorizationParams = function (options: any) {
  if (!options.subdomain) {
    return {};
  }
  return {
    subdomain: options.subdomain,
  };
};

export { Strategy };
