/* eslint-disable import/prefer-default-export */
/* eslint-disable no-param-reassign */
/* eslint-disable import/no-extraneous-dependencies */
const util = require('util');
const { OAuth2Strategy } = require('passport-oauth');

function Strategy(options: any, verify: any) {
  options = options || {};
  options.authorizationURL =
    options.authorizationURL || ' https://oauth.pipedrive.com/oauth/authorize';
  options.tokenURL = options.tokenURL || 'https://oauth.pipedrive.com/oauth/token';
  // @ts-expect-error: this seems to be of type any which is kind of not identified by linter
  OAuth2Strategy.call(this, options, verify);
}

util.inherits(Strategy, OAuth2Strategy);

Strategy.prototype.authorizationParams = function (options: any) {
  const params = {};
  if (options.state) {
    Object.assign(params, {
      state: options.state,
    });
  }
  return params;
};

export { Strategy };
