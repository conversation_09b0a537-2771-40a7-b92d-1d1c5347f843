/* eslint-disable import/prefer-default-export */
/* eslint-disable no-param-reassign */
const OAuth2Strategy = require('passport-oauth2').Strategy;
const util = require('util');

function Strategy(options: any, verify: any) {
  options = options || {};
  options.authorizationURL = options.authorizationURL || 'https://www.reddit.com/api/v1/authorize';
  options.tokenURL = options.tokenURL || 'https://www.reddit.com/api/v1/access_token';
  // @ts-expect-error: this seems to be of type any which is kind of not identified by linter
  OAuth2Strategy.call(this, options, verify);
}

util.inherits(Strategy, OAuth2Strategy);

Strategy.prototype.authorizationParams = function (options: any) {
  const params = {
    duration: 'permanent',
  };
  const { duration } = options;
  if (duration) {
    params.duration = duration;
  }
  return params;
};

export { Strategy };
