import { AccountDefinition, OAuthImplementation } from '../../types/oauth';
import { LinkedInImplementation } from '../../implementations/oauth/linkedin';
import { ZohoImplementation } from '../../implementations/oauth/zoho';

interface ImplementationConfig {
  BaseImplementation: new (
    accountDefinition: AccountDefinition,
    scope?: string[],
    options?: Record<string, any>,
  ) => OAuthImplementation;
  scope?: string[];
  options?: Record<string, any>;
}

export const IMPLEMENTATION_MAP: Record<string, ImplementationConfig> = {
  destination_zoho_oauth: {
    BaseImplementation: ZohoImplementation,
  },
  destination_zoho_dev_oauth: {
    BaseImplementation: ZohoImplementation,
  },
  destination_linkedin_ads_oauth: {
    BaseImplementation: LinkedInImplementation,
    scope: ['r_ads', 'r_liteprofile', 'rw_conversions'],
    options: {
      profileFields: ['formatted-name', 'email-address'],
    },
  },
  destination_linkedin_audience_oauth: {
    BaseImplementation: LinkedInImplementation,
    scope: ['r_liteprofile', 'rw_dmp_segments', 'r_ads'],
    options: {
      profileFields: ['formatted-name', 'email-address'],
    },
  },
};
