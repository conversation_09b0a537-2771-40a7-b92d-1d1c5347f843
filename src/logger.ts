import { LOGLEVELS, structuredLogger } from '@rudderstack/integrations-lib';

type loglevels = keyof typeof LOGLEVELS;
// LOGGER_IMPL can be `console` or `winston`
const loggerImpl = process.env.LOGGER_IMPL ?? 'winston';

const logLevel: loglevels = (process.env.LOG_LEVEL ?? 'info') as loglevels;

const logger = structuredLogger({
  level: logLevel,
  fillExcept: ['response', 'config'],
});

const getLogger = () => {
  switch (loggerImpl) {
    case 'console':
      return console;
    default:
      return logger; // Default to winston logger
  }
};

const debug = (msg: any, ...optionalParams: any[]) => {
  if (LOGLEVELS.debug <= LOGLEVELS[logLevel as loglevels]) {
    getLogger()?.debug(msg, optionalParams);
  }
};

const info = (msg: any, ...optionalParams: any[]) => {
  if (LOGLEVELS.info <= LOGLEVELS[logLevel as loglevels]) {
    getLogger()?.info(msg, optionalParams);
  }
};

const warn = (msg: any, ...optionalParams: any[]) => {
  if (LOGLEVELS.warn <= LOGLEVELS[logLevel as loglevels]) {
    getLogger()?.warn(msg, optionalParams);
  }
};

const error = (msg: any, ...optionalParams: any[]) => {
  if (LOGLEVELS.error <= LOGLEVELS[logLevel as loglevels]) {
    getLogger()?.error(msg, optionalParams);
  }
};

export default {
  debug,
  info,
  warn,
  error,
};
