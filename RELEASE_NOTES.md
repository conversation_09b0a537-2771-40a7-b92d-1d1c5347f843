## [1.2.1](https://github.com/rudderlabs/rudder-auth/compare/v1.2.0...v1.2.1) (2025-06-09)

### 🔧 Chores

* add ads and audiences scopes to both linkedin auth routes ([#461](https://github.com/rudderlabs/rudder-auth/issues/461)) ([e639be0](https://github.com/rudderlabs/rudder-auth/commit/e639be0560959a62070e05de6ccbc5621ea11f0f))
* **release:** prepare release 1.2.0 ([918b591](https://github.com/rudderlabs/rudder-auth/commit/918b591a771b8d05816c2d44d69d94d4749797d2))

