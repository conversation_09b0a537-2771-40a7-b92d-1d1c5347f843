import { Strategy } from '../../src/lib/passport/hubspot';

describe('Testing hubspot library functions', () => {
  it('Create a strategy and call authorizationParams function', () => {
    // @ts-ignore
    const strategy = new Strategy({ clientID: 1234 }, () => {});
    expect(strategy.authorizationParams({ state: 'new', optional_scope: 'login' })).toEqual({
      optional_scope: 'login',
      state: 'new',
    });
  });
});
