import { Strategy } from '../../src/lib/passport/zendesk_chat';

describe('Testing reddit library functions', () => {
  it('should return object with subdomain when options contain subdomain', () => {
    // @ts-ignore
    const strategy = new Strategy({ clientID: 1234 }, () => {});
    const options = { subdomain: 'example' };
    const result = strategy.authorizationParams(options);
    expect(result).toEqual(options);
  });
  it('should return empty object when options is empty object', () => {
    // @ts-ignore
    const strategy = new Strategy({ clientID: 1234 }, () => {});
    const result = strategy.authorizationParams({});
    expect(result).toEqual({});
  });
});
