import { Strategy } from '../../src/lib/passport/reddit';

describe('Testing reddit library functions', () => {
  it('should return default duration when options is empty', () => {
    // @ts-ignore
    const strategy = new Strategy({ clientID: '1234' }, {});
    const result = strategy.authorizationParams({});
    expect(result).toEqual({ duration: 'permanent' });
  });
  // handles null options gracefully
  it('should handle null options gracefully', () => {
    // @ts-ignore
    const strategy = new Strategy({ clientID: '1234' }, {});
    const result = strategy.authorizationParams({ duration: 'permanent' });
    expect(result).toEqual({ duration: 'permanent' });
  });
});
