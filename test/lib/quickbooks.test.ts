import { Strategy } from '../../src/lib/passport/quickbooks';
import axios from 'axios';

describe('Testing quickbooks library functions', () => {
  it('should retrieve user profile when access token is valid', async () => {
    const mockAxiosGet = jest.spyOn(axios, 'get').mockResolvedValue({
      data: { id: '123', name: '<PERSON>' },
    });
    const accessToken = 'validAccessToken';
    const done = jest.fn();

    // @ts-ignore
    const strategy = new Strategy({ clientID: 1234 }, {});
    await strategy.userProfile(accessToken, done);

    expect(mockAxiosGet).toHaveBeenCalledWith(
      'https://accounts.platform.intuit.com/v1/openid_connect/userinfo',
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/json',
        },
      },
    );
    expect(done).toHaveBeenCalledWith(null, { id: '123', name: '<PERSON>' });

    mockAxiosGet.mockRestore();
  });
  // handles invalid access token gracefully
  it('should handle invalid access token gracefully', async () => {
    const mockAxiosGet = jest.spyOn(axios, 'get').mockRejectedValue({
      response: { data: { error: 'invalid_token' } },
    });
    const accessToken = 'invalidAccessToken';
    const done = jest.fn();
    // @ts-ignore
    const strategy = new Strategy({ clientID: 1234 }, {});
    await strategy.userProfile(accessToken, done);

    expect(mockAxiosGet).toHaveBeenCalledWith(
      'https://accounts.platform.intuit.com/v1/openid_connect/userinfo',
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/json',
        },
      },
    );
    expect(done).toHaveBeenCalledTimes(0);

    mockAxiosGet.mockRestore();
  });
});
