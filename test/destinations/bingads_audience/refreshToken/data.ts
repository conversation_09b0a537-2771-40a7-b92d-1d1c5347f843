import Mock<PERSON>dapter from 'axios-mock-adapter';
import qs from 'querystring';
import { isMatch } from 'lodash';
import { AxiosHeaders } from 'axios';
import { testCaseData } from '../../../types';

const data: testCaseData[] = [
  {
    name: 'bingads_audience',
    id: 'bingads_audience_refreshToken',
    description: 'Test error scenario',
    feature: 'refreshToken',
    category: 'destination',
    version: 'v0',
    input: {
      request: {
        body: {
          refreshToken: 'dummyRefreshToken',
        },
        method: 'POST',
      },
    },
    output: {
      response: {
        status: 403,
        body: {
          status: 403,
          code: 'ref_token_invalid_grant',
          message: '[bingads_audience] "invalid_grant" error, refresh token has expired or revoked',
        },
      },
    },
    mockFns: (mockAdapter: MockAdapter) => {
      const grant_type = 'refresh_token';
      const reqData = qs.stringify({
        grant_type,
        refresh_token: 'dummyRefreshToken',
        client_id: 'the-client-id',
        client_secret: 'the-client-secret',
        scope: 'openid offline_access https://ads.microsoft.com/msads.manage',
      });
      mockAdapter
        .onPost('https://login.microsoftonline.com/common/oauth2/v2.0/token', reqData, {
          asymmetricMatch: (actual: AxiosHeaders) =>
            isMatch(actual, {
              'Content-Type': 'application/x-www-form-urlencoded',
            }),
        })
        .reply(400, {
          error: 'invalid_grant',
          error_description:
            "AADSTS70000: The provided value for the input parameter 'refresh_token' or 'assertion' is not valid. Trace ID: <> Correlation ID: <> Timestamp: 2024-05-20 07:13:06Z",
          error_codes: [70000],
          timestamp: '2024-05-20 07:13:06Z',
          trace_id: '<>',
          correlation_id: '<>',
          error_uri: 'https://login.microsoftonline.com/error?code=70000',
        });
    },
  },
];

export default data;
