import { testCaseData } from '../../../types';

const data: testCaseData[] = [
  {
    name: 'zoho',
    id: 'zoho_refreshToken_invalid_code',
    description:
      'should fail with ref_token_invalid_grant when refresh token return invalid_code error',
    feature: 'refreshToken',
    category: 'destination',
    version: 'v0',
    input: {
      request: {
        body: {
          refreshToken: 'invalid_refresh_token',
        },
        method: 'POST',
      },
    },
    output: {
      response: {
        status: 403,
        body: {
          status: 403,
          code: 'ref_token_invalid_grant',
          message:
            '[zoho] "invalid_code" error, refresh token has expired or revoked. Please try reconfiguring your account in the destination setup.',
        },
      },
    },
  },
];

export default data;
