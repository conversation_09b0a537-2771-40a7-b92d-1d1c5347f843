import MockAdapter from 'axios-mock-adapter';
import qs from 'querystring';
import { isMatch } from 'lodash';
import oAuthUtils from '../../../../src/lib/utils/oAuthUtils';
import { testCaseData } from '../../../types';

const data: testCaseData[] = [
  {
    name: 'reddit',
    description: 'Test error scenario',
    id: 'reddit_refreshToken',
    feature: 'refreshToken',
    category: 'destination',
    version: 'v0',
    input: {
      request: {
        body: {
          refreshToken: 'dummyRefreshToken',
        },
        method: 'POST',
      },
    },
    output: {
      response: {
        status: 500,
        body: {
          code: 'internalError',
          message: 'Network Error',
          status: 500,
        },
      },
    },
    mockFns: (mockAdapter: MockAdapter) => {
      jest.spyOn(oAuthUtils, 'getFromConfig').mockImplementationOnce(() => ({
        client_id: 'the-client-id-invalid',
        client_secret: 'the-client-secret',
      }));
      const grant_type = 'refresh_token';
      const reqData = qs.stringify({
        grant_type,
        refresh_token: 'dummyRefreshToken',
      });
      mockAdapter
        .onPost('https://www.reddit.com/api/v1/access_token', reqData, {
          asymmetricMatch(actual: any) {
            return isMatch(actual, {
              Authorization: 'Basic dGhlLWNsaWVudC1pZC1pbnZhbGlkOnRoZS1jbGllbnQtc2VjcmV0',
            });
          },
        })
        .networkError();
    },
  },
];

export default data;
