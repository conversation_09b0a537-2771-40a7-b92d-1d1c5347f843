import { join } from 'path';
import { globSync } from 'glob';
import MockAdapter from 'axios-mock-adapter';
import isMatch from 'lodash/isMatch';

import { networkData } from './types';

export const verifyRefreshTokenResponse = (received: any, expected: any) => {
  if (received?.body?.access_token) {
    expect(received.body.access_token).toEqual(expected.body.access_token);
  } else if (received?.body?.accessToken) {
    expect(received.body.accessToken).toEqual(expected.body.accessToken);
  } else if (received?.body?.refresh_token) {
    expect(received.body.refresh_token).toEqual(expected.body.refresh_token);
  } else if (received?.body?.refreshToken) {
    expect(received.body.refreshToken).toEqual(expected.body.refreshToken);
  } else if (received?.body?.expirationDate) {
    expect(received.body.expirationDate).toEqual(expected.body.expirationDate);
  } else if (
    received?.req?.path === '/tokens/bing_ads/refresh' ||
    received?.req?.path === '/tokens/destination/bing_ads/refresh'
  ) {
    expect(received.body.client_id).toEqual('the-client-id');
  } else {
    expect(received.body).toEqual(expected.body);
  }
};

export const getAllTestMockDataFilePaths = (dirPath: string): string[] => {
  const globPattern = join(dirPath, '**', 'network.ts');
  const testFilePaths = globSync(globPattern);
  return testFilePaths;
};
export const getTestDataFilePaths = (dirPath: string, opts: any): string[] => {
  const globPattern = join(dirPath, '**', 'data.ts');
  let testFilePaths = globSync(globPattern);
  if (opts.skipcommon === 'true') {
    testFilePaths = testFilePaths.filter((testFile) => !testFile.includes('common'));
  }
  return testFilePaths;
};
export const addMock = (mock: MockAdapter, axiosMock: networkData) => {
  const { url, method, data: reqData, params, ...opts } = axiosMock.httpReq;
  const { data, headers, status } = axiosMock.httpRes;

  const headersAsymMatch = {
    asymmetricMatch(actual: any) {
      return isMatch(actual, opts.headers);
    },
  };

  switch (method.toLowerCase()) {
    case 'get':
      // We are accepting parameters exclusively for mocking purposes and do not require a request body,
      // particularly for GET requests where it is typically unnecessary
      mock.onGet(url, { params }, headersAsymMatch).reply(status, data, headers);
      break;
    case 'delete':
      mock.onDelete(url, reqData, headersAsymMatch).reply(status, data, headers);
      break;
    case 'post':
      mock.onPost(url, reqData, headersAsymMatch).reply(status, data, headers);
      break;
    case 'patch':
      mock.onPatch(url, reqData, headersAsymMatch).reply(status, data, headers);
      break;
    case 'put':
      mock.onPut(url, reqData, headersAsymMatch).reply(status, data, headers);
      break;
    default:
      break;
  }
};

export const registerZohoMocks = (mockAdapter: MockAdapter) => {
  mockAdapter.onPost('https://accounts.zoho.com/oauth/v2/token').reply((config) => {
    const formData = config.data;
    const formDataString = formData.getBuffer().toString();
    const boundary = formData.getBoundary();
    const formDataLines = formDataString
      .split(boundary)
      .flatMap((line: string) => line.split('\r\n'))
      .filter((line: string) => line && !line.includes('--'));

    const pairs: Record<string, string> = {};
    for (let i = 0; i < formDataLines.length; i += 2) {
      const key = formDataLines[i].split('=')[1];
      const newKey = key.slice(1, key.length - 1);
      const value = formDataLines[i + 1];
      pairs[newKey] = value;
    }

    if (
      pairs.client_id === 'the-client-id' &&
      pairs.client_secret === 'the-client-secret' &&
      pairs.refresh_token === 'invalid_refresh_token'
    ) {
      return [200, { error: 'invalid_code' }];
    }

    return [200, { access_token: 1234, refresh_token: 1234, expires_in: 3600 }];
  });
};
