import { callback<PERSON><PERSON><PERSON>, authz<PERSON>and<PERSON> } from '../../../src/routes/auth/reddit';
import express from 'express';
import axios from 'axios';
import utils from '../../../src/lib/utils/oAuthUtils';
import { RudderCategory, roles } from '../../../src/lib/constants';
import passport = require('passport');

describe('Test reddit functions', () => {
  describe('Test callbackHandler function', () => {
    it('should exchange authorization code for access token successfully', async () => {
      const req = {
        url: '/auth/reddit/callback?code=valid_code',
        rudderCategory: 'someCategory',
      } as unknown as express.Request;
      const res = {
        send: jest.fn(),
        status: jest.fn().mockReturnThis(),
      } as unknown as express.Response;

      jest.spyOn(utils, 'oauthStrategyOptions').mockReturnValue({
        clientID: 'testClientID',
        clientSecret: 'testClientSecret',
        callbackURL: 'http://localhost/callback',
      });

      jest.spyOn(axios, 'request').mockResolvedValue({
        data: {
          access_token: 'testAccessToken',
          refresh_token: 'testRefreshToken',
        },
      });

      await callbackHandler(req, res);

      expect(res.send).toHaveBeenCalledWith({
        secret: {
          accessToken: 'testAccessToken',
          refreshToken: 'testRefreshToken',
        },
      });
    });
    it('should return 400 when authorization code is missing or invalid', async () => {
      const req = {
        url: '/auth/reddit/callback?code=invalid_code',
        rudderCategory: 'someCategory',
      } as unknown as express.Request;
      const res = {
        send: jest.fn(),
        status: jest.fn().mockReturnThis(),
      } as unknown as express.Response;

      jest.spyOn(axios, 'request').mockRejectedValue(new Error('Invalid code'));

      await callbackHandler(req, res);

      expect(axios.request).toHaveBeenCalledWith(
        expect.objectContaining({
          method: 'POST',
          url: 'https://www.reddit.com/api/v1/access_token',
        }),
      );
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith({
        success: false,
        err: new Error('Invalid code'),
      });
    });
  });
  describe('Test authzHandler function', () => {
    const role = roles.REDDIT;
    // Authenticates with correct strategy for RudderCategory.Destination
    it('should authenticate with correct strategy when rudderCategory is Destination', () => {
      const req = {
        rudderCategory: RudderCategory.Destination,
      } as express.Request;
      const res = {} as express.Response;
      const next = jest.fn();
      const strategyName = 'reddit_destination';

      jest.spyOn(utils, 'getStrategyName').mockReturnValue(strategyName);
      const passportAuthenticateSpy = jest
        .spyOn(passport, 'authenticate')
        .mockImplementation(() => () => {});

      authzHandler(req, res, next);

      const state = 'togorot';
      expect(utils.getStrategyName).toHaveBeenCalledWith(role, RudderCategory.Destination);
      expect(passport.authenticate).toHaveBeenCalledWith(strategyName, { state });
      expect(passportAuthenticateSpy).toHaveBeenCalled();
    });
    it('should handle missing rudderCategory in request', () => {
      const req = {} as express.Request;
      const res = {} as express.Response;
      const next = jest.fn();
      const strategyName = 'reddit';

      jest.spyOn(utils, 'getStrategyName').mockReturnValue(strategyName);
      const passportAuthenticateSpy = jest
        .spyOn(passport, 'authenticate')
        .mockImplementation(() => () => {});

      authzHandler(req, res, next);

      expect(utils.getStrategyName).toHaveBeenCalledWith(role, undefined);
      expect(passport.authenticate).toHaveBeenCalledWith(strategyName);
      expect(passportAuthenticateSpy).toHaveBeenCalled();
    });
  });
});
