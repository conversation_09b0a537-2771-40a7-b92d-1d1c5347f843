import { getStrategyNameWithRegion } from '../../../src/routes/auth/zoho';
import { roles, RudderCategory } from '../../../src/lib/constants';
import { Request, Response, NextFunction } from 'express';
import {
  DATA_CENTRE_BASE_ENDPOINTS_MAP,
  getZohoDC,
  RegionKeys,
} from '../../../src/routes/auth/utils/zoho';

describe('Zoho Authentication', () => {
  let mockAuthorize: jest.Mock;
  let mockAuthenticate: jest.Mock;
  let mockUse: jest.Mock;

  beforeEach(() => {
    jest.resetModules();
    jest.clearAllMocks();
    mockAuthorize = jest.fn(() => jest.fn());
    mockAuthenticate = jest.fn(() => jest.fn());
    mockUse = jest.fn();

    jest.mock('passport', () => ({
      use: mockUse,
      authenticate: mockAuthenticate,
      authorize: mockAuthorize,
    }));
    jest.mock('passport-zoho-crm', () => ({
      Strategy: jest.fn(),
    }));
  });

  describe('getStrategyNameWithRegion', () => {
    it('should return the correct strategy name with region', () => {
      const category = RudderCategory.Destination;
      const region = 'US';
      const expected = `${roles.ZOHO}_${category}__US`;

      const result = getStrategyNameWithRegion(category, region);

      expect(result).toBe(expected);
    });
  });

  describe('Passport strategy setup', () => {
    it('should set up passport strategies for all categories and regions', () => {
      jest.isolateModules(() => {
        require('../../../src/routes/auth/zoho');
      });
      const expectedCalls =
        Object.values(RudderCategory).length * Object.keys(DATA_CENTRE_BASE_ENDPOINTS_MAP).length;
      expect(mockUse).toHaveBeenCalledTimes(expectedCalls);
    });
  });

  describe('authzHandler', () => {
    it('should call passport.authenticate with the correct strategy name when region is not provided', () => {
      jest.isolateModules(() => {
        require('../../../src/routes/auth/zoho');
      });
      const { authzHandler } = require('../../../src/routes/auth/zoho');
      const mockReq = {
        rudderCategory: RudderCategory.Destination,
      } as unknown as Request;
      const mockRes = {} as Response;
      const mockNext = jest.fn() as NextFunction;

      authzHandler(mockReq, mockRes, mockNext);

      expect(mockAuthenticate).toHaveBeenCalledWith(
        `${roles.ZOHO}_${RudderCategory.Destination}__US`,
      );
    });
  });

  describe('callbackHandler', () => {
    it('should call passport.authorize with the correct strategy name', () => {
      jest.isolateModules(() => {
        require('../../../src/routes/auth/zoho');
      });
      const { callbackHandler } = require('../../../src/routes/auth/zoho');
      const mockReq = {
        rudderCategory: RudderCategory.Source,
      } as unknown as Request;
      const mockRes = {} as Response;
      const mockNext = jest.fn() as NextFunction;

      callbackHandler(mockReq, mockRes, mockNext);

      expect(mockAuthorize).toHaveBeenCalledWith(`${roles.ZOHO}__US`);
    });
  });

  describe('getZohoDC', () => {
    test('returns correct endpoint for valid regions', () => {
      const testCases: [RegionKeys, string][] = [
        ['US', 'https://accounts.zoho.com'],
        ['AU', 'https://accounts.zoho.com.au'],
        ['EU', 'https://accounts.zoho.eu'],
        ['IN', 'https://accounts.zoho.in'],
        ['CN', 'https://accounts.zoho.com.cn'],
        ['JP', 'https://accounts.zoho.jp'],
        ['CA', 'https://accounts.zohocloud.ca'],
      ];

      testCases.forEach(([region, expected]) => {
        expect(getZohoDC(region)).toBe(expected);
      });
    });

    test('returns undefined for invalid region(string not part of dcs)', () => {
      expect(getZohoDC('INVALID')).toBe(undefined);
    });

    test('returns undefined for invalid region(number)', () => {
      // @ts-ignore
      expect(getZohoDC(1)).toBe(undefined);
    });

    test('returns US endpoint for empty, undefined, or null string', () => {
      expect(getZohoDC('')).toBe('https://accounts.zoho.com');
      // @ts-ignore
      expect(getZohoDC(undefined)).toBe('https://accounts.zoho.com');
      // @ts-ignore
      expect(getZohoDC(null)).toBe('https://accounts.zoho.com');
    });

    test('is not case-sensitive', () => {
      expect(getZohoDC('us')).toBe('https://accounts.zoho.com');
      expect(getZohoDC('Us')).toBe('https://accounts.zoho.com');
    });
  });
});
