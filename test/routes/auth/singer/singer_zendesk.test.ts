import request from 'supertest';
import { clientCredsConfig } from 'client-configs';

import app from '../../../../src/app';
import { roles } from '../../../../src/lib/constants';
import oAuthUtils from '../../../../src/lib/utils/oAuthUtils';
import e from 'express';

describe('Singer Zendesk Auth Routes', () => {
  describe('auth routes tests', () => {
    const authTests = [
      {
        role: roles.SINGER_ZENDESK_SUPPORT,
        subdomain: 'support',
        host: 'support.zendesk.com',
        path: '/oauth/authorizations/new',
      },
      {
        role: roles.SINGER_ZENDESK_SUPPORT,
        subdomain: '',
        isError: true,
        status: 500,
      },
      {
        role: roles.SINGER_ZENDESK_CHAT,
        host: 'www.zopim.com',
        subdomain: 'chat',
        path: '/oauth2/authorizations/new',
      },
      {
        role: roles.SINGER_ZENDESK_CHAT,
        host: 'www.zopim.com',
        path: '/oauth2/authorizations/new',
        subdomain: '',
      },
    ];

    authTests.forEach(({ role, host, subdomain, path, isError, status }) => {
      it(`should test the auth route ${role}`, async () => {
        const response = await request(app).get(
          `/auth/source/${role}/authz?subdomain=${subdomain}`,
        );

        if (isError) {
          expect(response.status).toBe(status);
          return;
        }

        expect(response.status).toBe(302);
        expect(response.headers).toHaveProperty('location');
        const url = new URL(response.headers.location);
        expect(url.host).toBe(host);
        expect(url.pathname).toBe(path);
        expect(url.searchParams.get('response_type')).toBe('code');
        expect(url.searchParams.get('scope')).toBe('read');
        if (subdomain) {
          expect(url.searchParams.get('state')).toBe(
            Buffer.from(JSON.stringify({ subdomain })).toString('base64'),
          );
        } else if (!subdomain) {
          expect(url.searchParams.get('state')).toBeDefined();
        }

        expect(url.searchParams.get('redirect_uri')).toBe(
          `http://localhost:3000/auth/${role}/callback`,
        );
        const envKey = oAuthUtils.getEnvironmentVariableKey(role, '');
        const { client_id } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);
        expect(url.searchParams.get('client_id')).toBe(client_id);
      });
    });
  });

  describe('callback routes tests', () => {
    const callbackTests = [
      {
        role: roles.SINGER_ZENDESK_SUPPORT,
        state: Buffer.from(JSON.stringify({ subdomain: 'support' })).toString('base64'),
        host: 'support.zendesk.com',
        path: '/oauth/authorizations/new',
      },
      {
        role: roles.SINGER_ZENDESK_SUPPORT,
        isError: true,
        status: 500,
      },
      {
        role: roles.SINGER_ZENDESK_CHAT,
        state: Buffer.from(JSON.stringify({ subdomain: 'chat' })).toString('base64'),
        host: 'www.zopim.com',
        path: '/oauth2/authorizations/new',
      },
      {
        role: roles.SINGER_ZENDESK_CHAT,
        isError: true,
        status: 500,
      },
    ];

    callbackTests.forEach(({ role, state, host, path, isError, status }) => {
      it(`should test the callback route for ${role}`, async () => {
        const response = await request(app).get(`/auth/source/${role}/callback?state=${state}`);

        if (isError) {
          expect(response.status).toBe(status);
          return;
        }

        expect(response.status).toBe(302);
        expect(response.headers).toHaveProperty('location');
        const url = new URL(response.headers.location);
        expect(url.host).toBe(host);
        expect(url.pathname).toBe(path);
        expect(url.searchParams.get('response_type')).toBe('code');
        const envKey = oAuthUtils.getEnvironmentVariableKey(role, '');
        const { client_id } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);
        expect(url.searchParams.get('client_id')).toBe(client_id);

        expect(url.searchParams.get('redirect_uri')).toBe(
          `http://localhost:3000/auth/${role}/callback`,
        );
      });
    });
  });
});
