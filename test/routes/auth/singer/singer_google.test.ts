import request from 'supertest';
import { clientCredsConfig } from 'client-configs';

import app from '../../../../src/app';
import { roles } from '../../../../src/lib/constants';
import oAuthUtils from '../../../../src/lib/utils/oAuthUtils';

describe('Singer GoogleAuth Routes', () => {
  describe('auth routes tests', () => {
    const googleAuthTests = [
      {
        role: roles.SINGER_GOOGLE_SHEETS,
        scopes: [
          'https://www.googleapis.com/auth/userinfo.profile',
          'https://www.googleapis.com/auth/userinfo.email',
          'https://www.googleapis.com/auth/spreadsheets.readonly',
        ],
      },
      {
        role: roles.SINGER_GOOGLE_SEARCH_CONSOLE,
        scopes: [
          'https://www.googleapis.com/auth/webmasters.readonly',
          'https://www.googleapis.com/auth/userinfo.email',
          'https://www.googleapis.com/auth/userinfo.profile',
        ],
      },
      {
        role: roles.SINGER_GOOGLE_ANALYTICS,
        scopes: [
          'https://www.googleapis.com/auth/userinfo.profile',
          'https://www.googleapis.com/auth/userinfo.email',
          'https://www.googleapis.com/auth/analytics.readonly',
        ],
      },
      {
        role: roles.SINGER_GOOGLE_ADWORDS,
        scopes: [
          'https://www.googleapis.com/auth/userinfo.profile',
          'https://www.googleapis.com/auth/userinfo.email',
          'https://www.googleapis.com/auth/adwords',
          'https://www.googleapis.com/auth/analytics.readonly',
        ],
      },
    ];

    googleAuthTests.forEach(({ role, scopes }) => {
      it(`should test the auth route ${role}`, async () => {
        const response = await request(app).get(`/auth/source/${role}/authz`);
        expect(response.status).toBe(302);
        expect(response.headers).toHaveProperty('location');
        const url = new URL(response.headers.location);
        expect(url.host).toBe('accounts.google.com');
        expect(url.pathname).toBe('/o/oauth2/v2/auth');
        expect(url.searchParams.get('access_type')).toBe('offline');
        expect(url.searchParams.get('prompt')).toBe('consent');
        expect(url.searchParams.get('redirect_uri')).toBe(
          `http://localhost:3000/auth/${role}/callback`,
        );
        expect(url.searchParams.get('scope')).toBe(scopes.join(' '));
      });
    });
  });

  describe('callback routes tests', () => {
    const googleCallbackTests = [
      {
        role: roles.SINGER_GOOGLE_SHEETS,
      },
      {
        role: roles.SINGER_GOOGLE_SEARCH_CONSOLE,
      },
      {
        role: roles.SINGER_GOOGLE_ANALYTICS,
      },
      {
        role: roles.SINGER_GOOGLE_ADWORDS,
      },
    ];

    googleCallbackTests.forEach(({ role }) => {
      it(`should test the callback route for ${role}`, async () => {
        const response = await request(app).get(`/auth/source/${role}/callback`);
        expect(response.status).toBe(302);
        expect(response.headers).toHaveProperty('location');
        const url = new URL(response.headers.location);
        expect(url.host).toBe('accounts.google.com');
        expect(url.pathname).toBe('/o/oauth2/v2/auth');
        expect(url.searchParams.get('response_type')).toBe('code');
        const envKey = oAuthUtils.getEnvironmentVariableKey(role, '');
        const { client_id } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);
        expect(url.searchParams.get('client_id')).toBe(client_id);

        expect(url.searchParams.get('redirect_uri')).toBe(
          `http://localhost:3000/auth/${role}/callback`,
        );
      });
    });
  });
});
