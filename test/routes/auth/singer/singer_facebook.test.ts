import request from 'supertest';
import { clientCredsConfig } from 'client-configs';

import app from '../../../../src/app';
import { roles } from '../../../../src/lib/constants';
import oAuthUtils from '../../../../src/lib/utils/oAuthUtils';

describe('Singer Facebook Auth Routes', () => {
  describe('auth routes tests', () => {
    const googleAuthTests = [
      {
        role: roles.SINGER_FACEBOOK_MARKETING,
        scopes: [
          'ads_management, ads_read, business_management, pages_read_engagement, pages_show_list',
        ],
      },
    ];

    googleAuthTests.forEach(({ role, scopes }) => {
      it(`should test the auth route ${role}`, async () => {
        const response = await request(app).get(`/auth/source/${role}/authz`);
        expect(response.status).toBe(302);
        expect(response.headers).toHaveProperty('location');
        const url = new URL(response.headers.location);
        expect(url.host).toBe('www.facebook.com');
        expect(url.pathname).toBe('/v3.2/dialog/oauth');
        expect(url.searchParams.get('response_type')).toBe('code');
        expect(url.searchParams.get('redirect_uri')).toBe(
          `http://localhost:3000/auth/${role}/callback`,
        );
        expect(url.searchParams.get('scope')).toBe(scopes.join(' '));

        const envKey = oAuthUtils.getEnvironmentVariableKey(role, '');
        const { client_id } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);
        expect(url.searchParams.get('client_id')).toBe(client_id);
      });
    });
  });

  describe('callback routes tests', () => {
    const googleCallbackTests = [
      {
        role: roles.SINGER_FACEBOOK_MARKETING,
      },
    ];

    googleCallbackTests.forEach(({ role }) => {
      it(`should test the callback route for ${role}`, async () => {
        const response = await request(app).get(`/auth/source/${role}/callback`);
        expect(response.status).toBe(302);
        expect(response.headers).toHaveProperty('location');
        const url = new URL(response.headers.location);
        expect(url.host).toBe('www.facebook.com');
        expect(url.pathname).toBe('/v3.2/dialog/oauth');
        expect(url.searchParams.get('response_type')).toBe('code');
        const envKey = oAuthUtils.getEnvironmentVariableKey(role, '');
        const { client_id } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);
        expect(url.searchParams.get('client_id')).toBe(client_id);

        expect(url.searchParams.get('redirect_uri')).toBe(
          `http://localhost:3000/auth/${role}/callback`,
        );
      });
    });
  });
});
