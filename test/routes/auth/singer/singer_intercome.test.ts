import request from 'supertest';
import { clientCredsConfig } from 'client-configs';

import app from '../../../../src/app';
import { roles } from '../../../../src/lib/constants';
import oAuthUtils from '../../../../src/lib/utils/oAuthUtils';

describe('Singer Intercome Auth Routes', () => {
  describe('auth routes tests', () => {
    const authTests = [
      {
        role: roles.INTERCOM,
        host: 'app.intercom.io',
        path: '/oauth',
      },
    ];

    authTests.forEach(({ role, host, path }) => {
      it(`should test the auth route ${role}`, async () => {
        const response = await request(app).get(`/auth/source/${role}/authz`);

        expect(response.status).toBe(302);
        expect(response.headers).toHaveProperty('location');
        const url = new URL(response.headers.location);
        expect(url.host).toBe(host);
        expect(url.pathname).toBe(path);
        expect(url.searchParams.get('response_type')).toBe('code');
        expect(url.searchParams.get('redirect_uri')).toBe(
          `http://localhost:3000/auth/source/${role}/callback`,
        );
        const envKey = oAuthUtils.getEnvironmentVariableKey(role, '');
        const { client_id } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);
        expect(url.searchParams.get('client_id')).toBe(client_id);
      });
    });
  });

  describe('callback routes tests', () => {
    const callbackTests = [
      {
        role: roles.INTERCOM,
        state: Buffer.from(JSON.stringify({ subdomain: 'support' })).toString('base64'),
        host: 'app.intercom.io',
        path: '/oauth',
      },
      {
        role: roles.INTERCOM,
        isError: true,
        status: 400,
      },
    ];

    callbackTests.forEach(({ role, state, host, path, isError, status }) => {
      it(`should test the callback route for ${role}`, async () => {
        const response = await request(app).get(
          `/auth/source/${role}/callback${state ? `?state=${state}` : ''}`,
        );

        if (isError) {
          expect(response.status).toBe(status);
          return;
        }

        expect(response.status).toBe(302);
        expect(response.headers).toHaveProperty('location');
        const url = new URL(response.headers.location);
        expect(url.host).toBe(host);
        expect(url.pathname).toBe(path);
        expect(url.searchParams.get('response_type')).toBe('code');
        const envKey = oAuthUtils.getEnvironmentVariableKey(role, '');
        const { client_id } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);
        expect(url.searchParams.get('client_id')).toBe(client_id);

        expect(url.searchParams.get('redirect_uri')).toBe(
          `http://localhost:3000/auth/source/${role}/callback`,
        );
      });
    });
  });
});
