import request from 'supertest';
import { clientCredsConfig } from 'client-configs';

import app from '../../../../src/app';
import { roles } from '../../../../src/lib/constants';
import oAuthUtils from '../../../../src/lib/utils/oAuthUtils';

describe('Singer Bing ads Auth Routes', () => {
  describe('auth routes tests', () => {
    const googleAuthTests = [
      {
        role: roles.BING_ADS,
        scopes: ['https://ads.microsoft.com/msads.manage', 'offline_access', 'openid'],
      },
    ];

    googleAuthTests.forEach(({ role, scopes }) => {
      it(`should test the auth route ${role}`, async () => {
        const response = await request(app).get(`/auth/source/${role}/authz`);
        expect(response.status).toBe(302);
        expect(response.headers).toHaveProperty('location');
        const url = new URL(response.headers.location);
        expect(url.host).toBe('login.microsoftonline.com');
        expect(url.pathname).toBe('/common/oauth2/v2.0/authorize');
        expect(url.searchParams.get('response_type')).toBe('code');
        expect(url.searchParams.get('redirect_uri')).toBe(
          `http://localhost:3000/auth/${role}/callback`,
        );
        expect(url.searchParams.get('scope')).toBe(scopes.join(' '));

        const envKey = oAuthUtils.getEnvironmentVariableKey(role, '');
        const { client_id } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);
        expect(url.searchParams.get('client_id')).toBe(client_id);
      });
    });
  });

  describe('callback routes tests', () => {
    const googleCallbackTests = [
      {
        role: roles.BING_ADS,
      },
    ];

    googleCallbackTests.forEach(({ role }) => {
      it(`should test the callback route for ${role}`, async () => {
        const response = await request(app).get(`/auth/source/${role}/callback`);
        expect(response.status).toBe(302);
        expect(response.headers).toHaveProperty('location');
        const url = new URL(response.headers.location);
        expect(url.host).toBe('login.microsoftonline.com');
        expect(url.pathname).toBe('/common/oauth2/v2.0/authorize');
        expect(url.searchParams.get('response_type')).toBe('code');
        const envKey = oAuthUtils.getEnvironmentVariableKey(role, '');
        const { client_id } = oAuthUtils.getFromConfig<clientCredsConfig>(envKey);
        expect(url.searchParams.get('client_id')).toBe(client_id);

        expect(url.searchParams.get('redirect_uri')).toBe(
          `http://localhost:3000/auth/${role}/callback`,
        );
      });
    });
  });
});
