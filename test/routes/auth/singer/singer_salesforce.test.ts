import request from 'supertest';

import app from '../../../../src/app';
import { roles } from '../../../../src/lib/constants';

describe('Singer Salesforce Auth Routes', () => {
  describe('auth routes tests', () => {
    const googleAuthTests = [
      {
        role: roles.SINGER_SALESFORCE,
        host: 'login.salesforce.com',
        includeCategory: true,
      },
      {
        role: roles.SINGER_SALESFORCE,
        isSandbox: true,
        host: 'test.salesforce.com',
        includeCategory: false,
      },
    ];

    googleAuthTests.forEach(({ role, isSandbox, host, includeCategory }) => {
      it(`should test the auth route ${role}`, async () => {
        const response = await request(app).get(
          `/auth/source/${role}/authz${isSandbox ? '?is_sandbox=true' : ''}`,
        );
        expect(response.status).toBe(302);
        expect(response.headers).toHaveProperty('location');
        const url = new URL(response.headers.location);
        expect(url.host).toBe(host);
        expect(url.pathname).toBe('/services/oauth2/authorize');
        expect(url.searchParams.get('response_type')).toBe('code');
        if (includeCategory) {
          expect(url.searchParams.get('redirect_uri')).toBe(
            `http://localhost:3000/auth/source/${role}/callback`,
          );
        } else {
          expect(url.searchParams.get('redirect_uri')).toBe(
            `http://localhost:3000/auth/${role}/callback`,
          );
        }
      });
    });
  });

  describe('callback routes tests', () => {
    const googleCallbackTests = [
      {
        role: roles.SINGER_SALESFORCE,
        isSandbox: false,
        host: 'login.salesforce.com',
        includeCategory: true,
      },
      {
        role: roles.SINGER_SALESFORCE,
        state: Buffer.from(JSON.stringify({ is_sandbox: 'true' })).toString('base64'),
        isSandbox: true,
        host: 'test.salesforce.com',
        includeCategory: false,
      },
    ];

    googleCallbackTests.forEach(({ role, state, host, includeCategory }) => {
      it(`should test the callback route for ${role}`, async () => {
        const response = await request(app).get(
          `/auth/source/${role}/callback${state ? `?state=${state}` : ''}`,
        );
        expect(response.status).toBe(302);
        expect(response.headers).toHaveProperty('location');
        const url = new URL(response.headers.location);
        expect(url.host).toBe(host);
        expect(url.pathname).toBe('/services/oauth2/authorize');
        expect(url.searchParams.get('response_type')).toBe('code');

        if (includeCategory) {
          expect(url.searchParams.get('redirect_uri')).toBe(
            `http://localhost:3000/auth/source/${role}/callback`,
          );
        } else {
          expect(url.searchParams.get('redirect_uri')).toBe(
            `http://localhost:3000/auth/${role}/callback`,
          );
        }
      });
    });
  });
});
