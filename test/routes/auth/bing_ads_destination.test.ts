import { StrategyOptions } from 'passport-oauth2';
import { BingAdsDestination } from '../../../src/routes/auth/bing_ads_destination';
import axios, { AxiosError } from 'axios';
// import { mocked } from 'ts-jest/dist/utils/testing';

jest.mock('axios');
const axiosMock = jest.mocked(axios);
describe('Bingads Audience functions', () => {
  describe('Test userProfile function', () => {
    // Successfully fetch user profile with valid access token
    it('should fetch user profile successfully with valid access token', () => {
      const accessToken = 'validAccessToken';
      const mockResponse = {
        data: `<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
                      <s:Body>
                        <GetUserResponse xmlns="https://bingads.microsoft.com/Customer/v13">
                          <User>
                            <FirstName>John</FirstName>
                            <LastName>Doe</LastName>
                            <ContactInfo>
                              <Email><EMAIL></Email>
                            </ContactInfo>
                            <UserName>johndoe</UserName>
                          </User>
                        </GetUserResponse>
                      </s:Body>
                    </s:Envelope>`,
      };
      axiosMock.mockReturnValue(Promise.resolve(mockResponse));
      const bingAdsAudience = new BingAdsDestination(
        {
          authorizationURL:
            'https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=select_account',
          tokenURL: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
          clientID: '12345',
        } as StrategyOptions,
        jest.fn(),
        '',
      );
      bingAdsAudience.userProfile(accessToken, (err, profile) => {
        expect(profile).toMatchObject(
          expect.objectContaining({
            name: 'John Doe',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            userName: 'johndoe',
          }),
        );
        expect(err).toBeNull();
      });
    });
    it('should handle invalid or expired access token gracefully', () => {
      const accessToken = 'invalidAccessToken';
      const mockError = new Error('Invalid token');
      //   jest.spyOn(axios, 'post').mockRejectedValue(mockError);
      axiosMock.mockReturnValue(Promise.reject(mockError));
      const done = jest.fn();
      const bingAdsAudience = new BingAdsDestination(
        {
          authorizationURL:
            'https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=select_account',
          tokenURL: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
          clientID: '12345',
        } as StrategyOptions,
        () => {},
        '',
      );
      bingAdsAudience.userProfile(accessToken, (err, profile) => {
        expect(err?.message).toEqual('Invalid token');
        expect(profile).toBeUndefined();
      });
    });
    it('should handle fault scenario gracefully', () => {
      const accessToken = 'invalidAccessToken';
      const mockError = new Error('Invalid token');
      const mockResponse = {
        data: `<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
                      <s:Body>
                          <Fault>
                            <Message>Unable to get the userDetails</Message>
                          </Fault>
                      </s:Body>
                    </s:Envelope>`,
      };
      axiosMock.mockReturnValue(Promise.resolve(mockResponse));
      const done = jest.fn();
      const bingAdsAudience = new BingAdsDestination(
        {
          authorizationURL:
            'https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=select_account',
          tokenURL: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
          clientID: '12345',
        } as StrategyOptions,
        () => {},
        '',
      );
      bingAdsAudience.userProfile(accessToken, (err: any) => {
        expect(err?.message).toEqual('Failed to fetch user profile. With error: ');
        expect(err?.oauthError).toEqual('{"Message":"Unable to get the userDetails"}');
      });
    });
  });
});
