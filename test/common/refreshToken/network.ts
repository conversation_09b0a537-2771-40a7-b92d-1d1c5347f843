import qs from 'querystring';
import { networkData } from '../../types';

const networkCallsData: networkData[] = [
  {
    httpReq: {
      url: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
      method: 'POST',
      data: qs.stringify({
        grant_type: 'refresh_token',
        refresh_token: 'dummy',
        client_id: 'the-client-id',
        client_secret: 'the-client-secret',
        scope: 'openid offline_access https://ads.microsoft.com/msads.manage',
      }),
    },
    httpRes: { data: { access_token: 1234, refresh_token: 1234, expires_in: 3600 }, status: 200 },
  },
  {
    httpReq: {
      url: 'https://www.googleapis.com/oauth2/v3/token',
      method: 'POST',
    },
    httpRes: { data: { access_token: 1234, refresh_token: 1234 }, status: 200 },
  },
  {
    httpReq: {
      url: 'https://api.criteo.com/oauth2/token',
      method: 'POST',
    },
    httpRes: { data: { access_token: 1234, refresh_token: 1234 }, status: 200 },
  },
  {
    httpReq: {
      url: 'https://api.amazon.com/auth/o2/token',
      method: 'POST',
    },
    httpRes: { data: { access_token: 1234, refresh_token: 1234 }, status: 200 },
  },
  {
    httpReq: {
      url: 'https://api.hubapi.com/oauth/v1/token',
      method: 'POST',
    },
    httpRes: {
      data: { access_token: 1234, new_refresh_token: 1234, expires_in: 3600 },
      status: 200,
    },
  },
  {
    httpReq: {
      url: 'https://www.linkedin.com/oauth/v2/accessToken',
      method: 'POST',
    },
    httpRes: { data: { access_token: 1234, refresh_token: 1234, expires_in: 3600 }, status: 200 },
  },
  {
    httpReq: {
      url: 'https://www.randomurl.com/services/oauth2/token',
      method: 'POST',
    },
    httpRes: { data: { access_token: 1234, refresh_token: 1234 }, status: 200 },
  },
  {
    httpReq: {
      url: 'https://www.randomurl.com/services/oauth2/token',
      method: 'POST',
    },
    httpRes: { data: { access_token: 1234, refresh_token: 1234 }, status: 200 },
  },
  {
    httpReq: {
      url: 'https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer',
      method: 'POST',
    },
    httpRes: { data: { access_token: 1234, refresh_token: 1234, expires_in: 3600 }, status: 200 },
  },
  {
    httpReq: {
      url: 'https://www.reddit.com/api/v1/access_token',
      method: 'POST',
      headers: { Authorization: 'Basic dGhlLWNsaWVudC1pZDp0aGUtY2xpZW50LXNlY3JldA==' },
    },
    httpRes: { data: { access_token: 1234, refresh_token: 1234 }, status: 200 },
  },
  {
    httpReq: {
      url: 'https://accounts.snapchat.com/login/oauth2/access_token',
      method: 'POST',
    },
    httpRes: { data: { access_token: 1234, refresh_token: 1234 }, status: 200 },
  },
  {
    httpReq: {
      url: 'https://identity.xero.com/connect/token',
      method: 'POST',
    },
    httpRes: { data: { access_token: 1234, refresh_token: 1234, expires_in: 3600 }, status: 200 },
  },
  {
    httpReq: {
      url: 'https://oauth.yandex.com/token',
      method: 'POST',
    },
    httpRes: { data: { access_token: 1234, refresh_token: 1234, expires_in: 3600 }, status: 200 },
  },
];
export default networkCallsData;
