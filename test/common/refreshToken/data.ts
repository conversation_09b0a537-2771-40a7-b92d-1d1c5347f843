import fs from 'fs';
import { testCaseData } from '../../types';

const directory = './src/controllers/refreshTokenMethods';

const fileNames: string[] = [];

fs.readdirSync(directory).forEach((file: string) => {
  const fileName = file.substring(0, file.length - 3);
  if (fileName !== 'index') {
    fileNames.push(fileName);
  }
});

const data: testCaseData[] = [];

fileNames.forEach((fileName: string) => {
  data.push({
    id: `${fileName}_common`,
    name: fileName,
    description: `common test case for refreshToken for ${fileName}`,
    feature: 'refreshToken',
    category: 'destination',
    version: 'v0',
    input: {
      request: {
        body: {
          refreshToken: 'dummy',
          instance_url: 'https://www.randomurl.com',
          refrehs_token: 'dummy',
        },
        method: 'POST',
      },
    },
    output: {
      response: {
        status: 200,
        body: {
          access_token: 1234,
          refresh_token: 1234,
          accessToken: 1234,
          refreshToken: 1234,
          expirationDate: '2024-01-01T01:00:00.000Z',
        },
      },
    },
  });
});
export default data;
