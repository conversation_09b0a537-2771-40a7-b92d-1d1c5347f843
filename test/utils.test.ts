import config from 'config';
import { Request, Response, NextFunction } from 'express';
import oAuthUtils from '../src/lib/utils/oAuthUtils';
import { REF_TOKEN_INVALID_GRANT, RudderCategory, roles } from '../src/lib/constants';
import { AxiosError, AxiosResponse } from 'axios';

describe('getEnvironmentVariableKey tests', () => {
  const cases = [
    {
      input: {
        role: 'role',
        rudderCategory: 'destination',
      },
      description:
        'should return oauth.credentials.{role}.{rudderCategory} when rudderCategory is defined',
      output: 'oauth.credentials.role.destination',
    },
    {
      description: 'should return oauth.credentials.{role} when rudderCategory is undefined',
      input: {
        role: 'role',
      },
      output: 'oauth.credentials.role',
    },
    {
      description: 'should return oauth.credentials.{role} when rudderCategory is empty string',
      input: {
        role: 'role',
        rudderCategory: '',
      },
      output: 'oauth.credentials.role',
    },
  ];

  test.each(cases)('$description', ({ input, output: expectedOutput }) => {
    const out = oAuthUtils.getEnvironmentVariableKey(input.role, input.rudderCategory);
    expect(out).toEqual(expectedOutput);
  });
});

describe('handleInvalidGrantError tests', () => {
  const newError = (respData: any, respStatus: any): AxiosError<any> => {
    const response: AxiosResponse = {
      data: respData,
      status: respStatus,
    } as AxiosResponse;
    const err = {
      config: {},
      request: {},
      response: response,
    } as AxiosError<any>;
    return err;
  };

  test('should throw InvalidGrantError when criteo_audience gets invalid_grant response', () => {
    const err = newError('invalid_grant', 403);

    try {
      oAuthUtils.handleInvalidGrantError(roles.CRITEO_AUDIENCE, err);
    } catch (error: any) {
      expect(error).not.toBeNull();
      expect(error).toHaveProperty('message');
      expect(error).toHaveProperty('status');
      expect(error).toHaveProperty('statusText');
      expect(error).toHaveProperty('code');
      expect(error.message).toEqual(
        `[${roles.CRITEO_AUDIENCE}] "invalid_grant" error, refresh token has expired or revoked`,
      );
      expect(error.status).toEqual(403);
      expect(error.statusText).toEqual('Forbidden');
      expect(error.code).toEqual(REF_TOKEN_INVALID_GRANT);
    }
  });

  test('should throw InvalidGrantError when criteo_audience does not invalid_grant response', () => {
    const err = newError({ msg: 'other error' }, 401);

    try {
      oAuthUtils.handleInvalidGrantError(roles.CRITEO_AUDIENCE, err);
    } catch (error: any) {
      expect(error).not.toBeNull();
      expect(error).not.toHaveProperty('message');
      expect(error).not.toHaveProperty('status');
      expect(error).not.toHaveProperty('statusText');
      expect(error).not.toHaveProperty('code');
      expect(error).toEqual(err);
    }
  });
  test('should throw InvalidGrantError when salesforce gets invalid_grant response', () => {
    const err = newError({ error: 'invalid_grant' }, 403);

    try {
      oAuthUtils.handleInvalidGrantError(roles.SALESFORCE, err);
    } catch (error: any) {
      expect(error).not.toBeNull();
      expect(error).toHaveProperty('message');
      expect(error).toHaveProperty('status');
      expect(error).toHaveProperty('statusText');
      expect(error).toHaveProperty('code');
      expect(error.message).toEqual(
        `[${roles.SALESFORCE}] "invalid_grant" error, refresh token has expired or revoked`,
      );
      expect(error.status).toEqual(403);
      expect(error.statusText).toEqual('Forbidden');
      expect(error.code).toEqual(REF_TOKEN_INVALID_GRANT);
    }
  });
});

describe('getStrategyName tests', () => {
  const cases = [
    {
      description: 'should return {role}.{rudderCategory} when category is sent as `destination`',
      input: {
        role: 'role',
        rudderCategory: 'destination',
      },
      output: 'role_destination',
    },
    {
      description: 'should return {role} when category is sent as `source`',
      input: {
        role: 'role',
        rudderCategory: 'source',
      },
      output: 'role',
    },
    {
      description: 'should return {role} when category is not sent',
      input: {
        role: 'role',
      },
      output: 'role',
    },
    {
      description:
        'should return {role}.{rudderCategory} when category is sent as RudderCategory.destination',
      input: {
        role: 'role',
        rudderCategory: RudderCategory.Destination,
      },
      output: 'role_destination',
    },
  ];
  test.each(cases)('$description', ({ input, output: expected }) => {
    expect(oAuthUtils.getStrategyName(input.role, input.rudderCategory)).toEqual(expected);
  });
});

describe('callbackURLByRole tests', () => {
  const configHasSpy = jest.spyOn(config, 'has');
  const configGetSpy = jest.spyOn(config, 'get');
  configGetSpy.mockImplementation((r) => {
    if (r === `oauth.credentials.role.callback_url`) {
      return 'http://localhost:3000/auth/my/callback';
    }
    if (r === 'oauth.base_redirect_url') {
      return 'http://locahost:3000';
    }
  });

  const cases = [
    {
      description: 'should return proper callback_url when role is sent',
      input: {
        role: 'role',
        mockFns: () => {
          configHasSpy.mockReturnValueOnce(true);
        },
      },
      expected: 'http://localhost:3000/auth/my/callback',
    },
    {
      description: 'should return proper callback_url using redirect_url when role is not sent',
      input: {
        mockFns: () => {
          configHasSpy.mockReturnValueOnce(false);
        },
      },
      expected: 'http://locahost:3000/auth//callback',
    },
    {
      description:
        'should return proper callback_url using redirect_url when role is sent but not present in config',
      input: {
        role: 'role',
        mockFns: () => {
          configHasSpy.mockReturnValueOnce(false);
        },
      },
      expected: 'http://locahost:3000/auth/role/callback',
    },
  ];
  test.each(cases)('$description', ({ input, expected }) => {
    input?.mockFns?.();
    expect(oAuthUtils.callbackURLByRole(input.role || '')).toEqual(expected);
  });
  afterAll(() => {
    jest.clearAllMocks();
  });
});

describe('validateRudderCategoryMiddleware tests', () => {
  test('should not return 404 status-code when valid rudderCategory is sent', () => {
    // @ts-ignore
    const req = {
      params: {
        rudderCategory: 'destination',
      },
    } as Request;
    oAuthUtils.validateRudderCategoryMiddleware(req, {} as Response, () => {});
    expect(req).toHaveProperty('newRoute');
    expect(req).toHaveProperty('rudderCategory');
    expect(req.newRoute).toBeTruthy();
    expect(req.rudderCategory).toEqual(RudderCategory.Destination);
  });
  test('should return 404 when no rudderCategory is sent in req.params', () => {
    // @ts-ignore
    const req = {
      params: {},
    } as Request;
    // @ts-ignore
    const res = {
      status: jest.fn((st) => {
        res.statusCode = st;
        return res;
      }),
      send: jest.fn(),
    } as Response;
    oAuthUtils.validateRudderCategoryMiddleware(req, res, () => {});
    expect(res.statusCode).toEqual(404);
    expect(res.send).toHaveBeenCalled();
    expect(res.send).toHaveBeenCalledWith('The requested url not found');
  });
  test('should return 404 when an invalid rudderCategory is sent in req.params', () => {
    // @ts-ignore
    const req = {
      params: {
        rudderCategory: 'something',
      },
    } as Request;
    // @ts-ignore
    const res = {
      status: jest.fn((st) => {
        res.statusCode = st;
        return res;
      }),
      send: jest.fn(),
    } as Response;
    oAuthUtils.validateRudderCategoryMiddleware(req, res, () => {});
    expect(res.statusCode).toEqual(404);
    expect(res.send).toHaveBeenCalled();
    expect(res.send).toHaveBeenCalledWith('The requested url not found');
  });
});

describe('callbackUrlByRoleAndCategory tests', () => {
  test('should return the callback URL for a given role and category if it exists in the config file', () => {
    const role = 'bingads_audience';
    const category = RudderCategory.Destination;
    const expectedUrl = 'http://locahost:3000/auth/destination/bingads_audience/callback';

    const result = oAuthUtils.callbackUrlByRoleAndCategory(role, category);

    expect(result).toBe(expectedUrl);
  });
  test('should returnthe callback URL for a given role without category if it exists in the config file', () => {
    const expectedUrl = 'http://locahost:3000/auth/test/callback';

    const result = oAuthUtils.callbackUrlByRoleAndCategory('test', undefined);

    expect(result).toBe(expectedUrl);
  });
});

describe('toFormData tests', () => {
  test('should correctly convert a simple object to form data when given a simple object', () => {
    const obj = {
      name: 'John',
      age: 30,
      email: '<EMAIL>',
    };

    const result = oAuthUtils.toFormData(obj);

    expect(result).toBe('name=John&age=30&email=<EMAIL>');
  });
  test('should handle object with nested objects and return the correct form data string when given an object with nested objects', () => {
    const obj = {
      name: 'John',
      age: 30,
      address: {
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
      },
    };

    const result = oAuthUtils.toFormData(obj);

    expect(result).toBe(
      'name=John&age=30&address={"street":"123 Main St","city":"New York","state":"NY"}',
    );
  });
  test('should handle object with nested objects and return the correct form data string when given an object with multiple levels of nesting', () => {
    const obj = {
      name: 'John',
      age: 30,
      address: {
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        country: {
          name: 'United States',
          code: 'US',
        },
      },
    };

    const result = oAuthUtils.toFormData(obj);

    expect(result).toBe(
      'name=John&age=30&address={"street":"123 Main St","city":"New York","state":"NY","country":{"name":"United States","code":"US"}}',
    );
  });
  test('should handle object with nested objects and return the correct form data string when given an object with arrays and nested objects', () => {
    const obj = {
      name: 'John',
      age: 30,
      address: {
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
      },
      hobbies: ['reading', 'running', 'painting'],
      friends: [
        { name: 'Jane', age: 28 },
        { name: 'Bob', age: 32 },
      ],
    };

    const result = oAuthUtils.toFormData(obj);

    expect(result).toBe(
      'name=John&age=30&address={"street":"123 Main St","city":"New York","state":"NY"}&hobbies=["reading","running","painting"]&friends=[{"name":"Jane","age":28},{"name":"Bob","age":32}]',
    );
  });
});

describe('accountInfo tests', () => {
  test('should return an object with metadata and accountName properties when profile parameter is not provided', () => {
    const result = oAuthUtils.accountInfo();
    expect(result).toEqual({
      metadata: {
        userId: undefined,
        email: undefined,
        displayName: undefined,
        userName: undefined,
      },
      accountName: undefined,
    });
  });
  test('should return an object with metadata and accountName properties when profile parameter and _json property with only some of the userId, displayName and email properties are provided', () => {
    const profile = {
      _json: {
        userId: '123',
        displayName: 'John Doe',
        email: '<EMAIL>',
      },
    };
    const result = oAuthUtils.accountInfo(profile);
    expect(result).toEqual({
      metadata: {
        userId: '123',
        email: '<EMAIL>',
        displayName: 'John Doe',
        userName: '<EMAIL>',
      },
      accountName: 'John Doe',
    });
  });
  test('should return an object with metadata and accountName properties when profile parameter and _json property with all of the userId, displayName and email properties are provided', () => {
    const profile = {
      _json: {
        userId: '123',
        displayName: 'John Doe',
        email: '<EMAIL>',
      },
      userId: '456',
      displayName: 'Jane Smith',
      email: '<EMAIL>',
    };
    const result = oAuthUtils.accountInfo(profile);
    expect(result).toEqual({
      metadata: {
        userId: '456',
        email: '<EMAIL>',
        displayName: 'Jane Smith',
        userName: '<EMAIL>',
      },
      accountName: 'Jane Smith',
    });
  });
  test('should return an object with metadata and accountName properties when profile parameter and _json property with only the displayName property are provided', () => {
    const profile = {
      _json: {
        displayName: 'John Doe',
      },
      userId: '456',
      displayName: 'Jane Smith',
    };
    const result = oAuthUtils.accountInfo(profile);
    expect(result).toEqual({
      metadata: {
        userId: '456',
        displayName: 'Jane Smith',
        userName: 'Jane Smith',
      },
      accountName: 'Jane Smith',
    });
  });

  test('should return an object with metadata userName when profile parameter and _json property with only the userId property are provided', () => {
    const profile = {
      _json: {
        userId: '123',
      },
      userId: '456',
    };
    const result = oAuthUtils.accountInfo(profile);
    expect(result).toEqual({
      metadata: {
        userId: '456',
        displayName: undefined,
        email: undefined,
        userName: '456',
      },
      accountName: undefined,
    });
  });
});

describe('getFromConfig tests', () => {
  test('should return the value from config when a valid key is provided', () => {
    const key = 'someKey';
    const value = 'someValue';
    jest.spyOn(config, 'get').mockReturnValue(value);

    const result = oAuthUtils.getFromConfig(key);

    expect(result).toBe(value);
    expect(config.get).toHaveBeenCalledWith(key);
  });
});

describe('oauthStrategyOptions tests', () => {
  test('should return a TSOAuthStrategyOptions object with clientID, clientSecret, and callbackURL properties when category is destination', () => {
    // Arrange
    const role = 'destination_xyz';
    const rudderCategory = 'destination';

    // Act
    const result = oAuthUtils.oauthStrategyOptions(role, rudderCategory);

    // Assert
    expect(result).toHaveProperty('clientID');
    expect(result).toHaveProperty('clientSecret');
    expect(result).toHaveProperty('callbackURL');
    expect(result.callbackURL).toBe('someValue/auth/destination/destination_xyz/callback');
  });
  test('should return a TSOAuthStrategyOptions object with clientID, clientSecret, and callbackURL properties when category is source', () => {
    // Arrange
    const role = 'destination_xyz';
    const rudderCategory = 'source';

    // Act
    const result = oAuthUtils.oauthStrategyOptions(role, rudderCategory);

    // Assert
    expect(result).toHaveProperty('clientID');
    expect(result).toHaveProperty('clientSecret');
    expect(result).toHaveProperty('callbackURL');
    expect(result.callbackURL).toBe('someValue/auth/source/destination_xyz/callback');
  });
});

describe('oauthStrategyConsumerOptions tests', () => {
  test('should return a TSOAuthStrategyConsumerOptions object with consumerKey, consumerSecret, callbackURL and skipExtendedUserProfile properties when category is destination', () => {
    // Arrange
    const role = 'destination_xyz';
    const rudderCategory = 'destination';

    // Act
    const result = oAuthUtils.oauthStrategyConsumerOptions(role, rudderCategory);

    // Assert
    expect(result).toHaveProperty('consumerKey');
    expect(result).toHaveProperty('consumerSecret');
    expect(result).toHaveProperty('callbackURL');
    expect(result).toHaveProperty('skipExtendedUserProfile');
    expect(result.callbackURL).toBe('someValue/auth/destination/destination_xyz/callback');
  });
});
