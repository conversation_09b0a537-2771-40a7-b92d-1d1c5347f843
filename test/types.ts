import MockAdapter from 'axios-mock-adapter';

export type input = {
  request: {
    body: any;
    method: string;
  };
};
export type output = {
  response: {
    status: number;
    body: any;
    headers?: any;
  };
};

export type testCaseData = {
  name: string;
  description: string;
  category?: string;
  feature: string;
  version: string;
  input: input;
  output: output;
  id: string;
  mockFns?: (mockAdapter: MockAdapter) => void;
};
export type httpReq = {
  url: string;
  method: string;
  data?: any;
  headers?: any;
  params?: any;
};
export type httpRes = {
  data: any;
  status: number;
  headers?: any;
};
export type networkData = {
  httpReq: httpReq;
  httpRes: httpRes;
};
