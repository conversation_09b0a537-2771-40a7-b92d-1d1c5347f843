import request from 'supertest';
import app from '../src/app';
import { Server } from 'http';
import { Command } from 'commander';
import MockAdapter from 'axios-mock-adapter';
import axios from 'axios';
import { createHttpTerminator } from 'http-terminator';
import { networkData, testCaseData } from './types';
import {
  addMock,
  getAllTestMockDataFilePaths,
  getTestDataFilePaths,
  registerZohoMocks,
  verifyRefreshTokenResponse,
} from './testUtils';

// To run single integration test cases
// npm run test -- component --integration=reddit
// npm run test -- component --integration=reddit --feature=refreshToken

// To run a specific test cases we can use id
// npm run test -- component --id=reddit_refreshToken
// npm run test -- component --id=pardot_common

// To run all the test cases of a specific category
// npm run test -- component --category=destination
// npm run test -- component --category=source

// To skip common test cases
// npm run test -- component --skipcommon=true

// To run all the test cases of a specific feature
// npm run test -- component --feature=refreshToken

// To run all the test cases of a specific feature and category
// npm run test -- component --feature=refreshToken --category=destination

const command = new Command();
command
  .allowUnknownOption()
  .option('--integration <string>', 'Enter Destination Name')
  .option('--category <string>', 'Enter Category name(source, destination)')
  .option('--id <string>', 'Enter unique "Id" of the test case you want to run')
  .option('--feature <string>', 'Enter feature name')
  .option('--skipcommon <boolean>', 'Skip common test cases')
  .parse();
const opts = command.opts();
let server: Server;
server = app.listen();
beforeAll(async () => {
  // Issue when you use useFakeTimers with supertest https://github.com/ladjs/supertest/issues/788
  // jest.useFakeTimers().setSystemTime(new Date('2024-01-01'));
});
afterAll(async () => {
  await createHttpTerminator({ server }).terminate();
});
export const getMockHttpCallsData = (filePath: string): networkData[] =>
  require(filePath).default as networkData[];
const rootDir = __dirname;
const allTestDataFilePaths = getTestDataFilePaths(rootDir, opts);

let mockAdapter = new MockAdapter(axios, { onNoMatch: 'throwException' });
const registerAxiosMocks = (axiosMocks: networkData[]) => {
  axiosMocks.forEach((axiosMock) => addMock(mockAdapter, axiosMock));
};

// // all the axios requests will be stored in this map
const allTestMockDataFilePaths = getAllTestMockDataFilePaths(__dirname);
const allAxiosRequests = allTestMockDataFilePaths
  .map((currPath) => {
    const mockNetworkCallsData: networkData[] = getMockHttpCallsData(currPath);
    return mockNetworkCallsData;
  })
  .flat();
registerAxiosMocks(allAxiosRequests);
// register zoho mocks
registerZohoMocks(mockAdapter);

const testRoute = async (route: string, tcData: testCaseData) => {
  const inputReq = tcData.input.request;
  const { body } = inputReq;
  let testRequest: request.Test;
  // as we are supporting tests for refresh token only, we are only supporting POST requests
  switch (inputReq.method) {
    default:
      testRequest = request(server).post(route);
      break;
  }

  const response = await testRequest.send(body);
  const outputResp = tcData.output.response || ({} as any);

  expect(response.status).toEqual(outputResp.status);

  if (tcData.feature === 'refreshToken') {
    verifyRefreshTokenResponse(response, outputResp);
  }
};

export const getTestData = (filePath: string): testCaseData[] => {
  return require(filePath)['default'] as testCaseData[];
};

const applyFilter = (testData: testCaseData[], opts: any) => {
  if (opts.category) {
    testData = testData.filter((data) => data['category'] === opts.category);
  }
  if (opts.feature) {
    testData = testData.filter((data) => data['feature'] === opts.feature);
  }
  if (opts.integration) {
    testData = testData.filter((data) => data['name'] === opts.integration);
  }
  if (opts.id) {
    testData = testData.filter((data) => data['id'] === opts.id);
  }
  return testData;
};

describe.each(allTestDataFilePaths)('%s Tests', (testDataPath) => {
  // add special mocks for specific destinations
  let testData: testCaseData[] = getTestData(testDataPath);
  const doubledTestData = testData
    .map((tc) => {
      return [{ ...tc }, { ...tc, category: 'undefined' }];
    })
    .flat();
  const filteredTestData = applyFilter(doubledTestData, opts);

  if (filteredTestData.length !== 0) {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    describe(filteredTestData[0].name, () => {
      test.each(filteredTestData)('$feature, $category -> $description', async (tcData) => {
        tcData?.mockFns?.(mockAdapter);

        switch (tcData.feature) {
          case 'refreshToken':
            if (tcData.category && tcData.category !== 'undefined') {
              await testRoute(`/tokens/${tcData.category}/${tcData.name}/refresh`, tcData);
              break;
            }
            await testRoute(`/tokens/${tcData.name}/refresh`, tcData);
            break;
          default:
            console.log('Invalid feature');
            // Intentionally fail the test case
            expect(true).toEqual(false);
            break;
        }
      });
    });
  }
});
