openapi: 3.0.0
info:
  title: Auth Service
  description: Authentication service for handling OAuth flows with various providers
  version: 1.0.0

servers:
  - url: /

paths:
  /auth/{rudderCategory}/{role}/authz:
    get:
      summary: Get authorization URL for OAuth flow
      description: Initiates the OAuth flow for the specified provider. Note - This endpoint returns a 302 redirect to the provider's authorization page. When testing, use cURL with -i flag or disable automatic redirect following to see the actual URL.
      parameters:
        - name: rudderCategory
          in: path
          required: true
          schema:
            type: string
          description: Resource category (e.g., source, destination)
        - name: role
          in: path
          required: true
          schema:
            type: string
          description: The OAuth provider (e.g., google, facebook, linkedin)
      responses:
        '302':
          description: Redirects to provider's authorization page
          headers:
            Location:
              schema:
                type: string
              description: The URL to redirect to
              example: https://accounts.google.com/o/oauth2/v2/auth?response_type=code&client_id=...
        '400':
          description: Bad request (e.g., missing required parameters)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/{rudderCategory}/{role}/callback:
    get:
      summary: OAuth callback endpoint
      description: Handles the OAuth callback from providers
      parameters:
        - name: rudderCategory
          in: path
          required: true
          schema:
            type: string
          description: Resource category (e.g., source, destination)
        - name: role
          in: path
          required: true
          schema:
            type: string
          description: The OAuth provider
        - name: code
          in: query
          schema:
            type: string
          description: Authorization code from provider
        - name: state
          in: query
          schema:
            type: string
          description: State parameter for security validation
      responses:
        '200':
          description: Successful authentication
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Authentication failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /tokens/{rudderCategory}/{role}/refresh:
    post:
      summary: Refresh access token
      description: Get a new access token using a refresh token
      parameters:
        - name: rudderCategory
          in: path
          required: true
          schema:
            type: string
          description: Resource category (e.g., source, destination)
        - name: role
          in: path
          required: true
          schema:
            type: string
          description: The OAuth provider
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refreshToken
              properties:
                refreshToken:
                  type: string
                  description: The refresh token to use
      responses:
        '200':
          description: New credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshResponse'
        '400':
          description: Refresh failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Provider not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/v1/url:
    post:
      tags:
        - OAuth V1
      summary: Initiate OAuth flow
      description: Initiates the OAuth authentication flow for the specified service
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - accountDefinition
                - account
              properties:
                account:
                  $ref: '#/components/schemas/Account'
                accountDefinition:
                  $ref: '#/components/schemas/AccountDefinition'
      responses:
        '302':
          description: Redirects to provider's authorization page
          headers:
            Location:
              schema:
                type: string
              description: The URL to redirect to
        '500':
          $ref: '#/components/responses/InternalError'

  /auth/v1/callback:
    post:
      tags:
        - OAuth V1
      summary: OAuth callback endpoint
      description: Handles the OAuth callback from the provider
      parameters:
        - in: query
          name: code
          schema:
            type: string
          description: Authorization code from the provider
        - in: query
          name: state
          schema:
            type: string
          description: State parameter for security validation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - accountDefinition
                - account
              properties:
                accountDefinition:
                  $ref: '#/components/schemas/AccountDefinition'
                account:
                  $ref: '#/components/schemas/Account'
      responses:
        '200':
          description: Successfully authenticated
          content:
            application/json:
              schema:
                type: object
                properties:
                  metadata:
                    type: object
                    description: User profile information
                  secret:
                    type: object
                    properties:
                      accessToken:
                        type: string
                      refreshToken:
                        type: string
        '500':
          $ref: '#/components/responses/InternalError'

  /auth/v1/refresh:
    post:
      tags:
        - OAuth V1
      summary: Refresh OAuth token
      description: Refreshes the OAuth access token using the refresh token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - accountDefinition
                - account
              properties:
                account:
                  $ref: '#/components/schemas/Account'
                accountDefinition:
                  $ref: '#/components/schemas/AccountDefinition'
      responses:
        '200':
          description: Successfully refreshed token
          content:
            application/json:
              schema:
                type: object
                properties:
                  secret:
                    type: object
                    description: Refreshed OAuth token and properties
                    properties:
                      accessToken:
                        type: string
                      refreshToken:
                        type: string
        '500':
          $ref: '#/components/responses/InternalError'

components:
  schemas:
    AuthResponse:
      type: object
      properties:
        role:
          type: string
          description: The OAuth provider
        profile:
          type: object
          description: User profile from provider
        secret:
          type: object
          properties:
            accessToken:
              type: string
            refreshToken:
              type: string
            expirationDate:
              type: string
              format: date-time
          description: OAuth tokens and related information

    RefreshResponse:
      type: object
      properties:
        access_token:
          type: string
        token_type:
          type: string
        expires_in:
          type: integer
        developer_token:
          type: string
          description: Available for specific providers (e.g., Google Adwords)

    Error:
      type: object
      properties:
        error:
          type: object
          properties:
            code:
              type: string
              enum:
                - internal_error

            message:
              type: string
              description: Detailed error message

    Account:
      type: object
      required:
        - options
      properties:
        options:
          type: object
          description: Account Options
          example: { region: 'US' }

    AccountDefinition:
      type: object
      required:
        - type
        - category
        - authenticationType
      properties:
        type:
          type: string
          description: The OAuth provider type
          example: 'zoho'
        category:
          type: string
          description: Resource category
          example: 'destination'
        authenticationType:
          type: string
          description: Authentication type
          example: 'oauth'

  responses:
    InternalError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
