oauth:
  base_redirect_url: OAUTH_BASE_REDIRECT_URL
  session:
    enabled: OAUTH_SESSION_ENABLED
    mongo: OAUTH_SESSION_MONGO
    secret: OAUTH_SESSION_SECRET
  credentials:
    google:
      client_id: GOO<PERSON><PERSON>_CLIENT_ID
      client_secret: GOO<PERSON><PERSON>_CLIENT_SECRET
    google_search_console:
      client_id: GOO<PERSON><PERSON>_SEARCH_CONSOLE_CLIENT_ID
      client_secret: GOOG<PERSON>_SEARCH_CONSOLE_CLIENT_SECRET
    google_reviews:
      client_id: GOOGLE_REVIEWS_CLIENT_ID
      client_secret: GOOGLE_REVIEWS_CLIENT_SECRET
    bing_ads:
      client_id: BING_ADS_CLIENT_ID
      client_secret: BING_ADS_CLIENT_SECRET
      developer_token: BING_DEVELOPER_TOKEN
    google_dfp:
      client_id: GOOGLE_DFP_CLIENT_ID
      client_secret: GOOG<PERSON>_DFP_CLIENT_SECRET
    bigquery:
      client_id: BIGQUERY_CLIENT_ID
      client_secret: BIGQUERY_CLIENT_SECRET
    salesforce:
      client_id: 'SALESFORCE_CLIENT_ID'
      client_secret: 'SALESFORCE_CLIENT_SECRET'
      destination:
        client_id: 'SALESFORCE_CLIENT_ID_DESTINATION'
        client_secret: 'SALESFORCE_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'SALESFORCE_CLIENT_ID_SOURCE'
        client_secret: 'SALESFORCE_CLIENT_SECRET_SOURCE'
    salesforce_sandbox:
      client_id: 'SALESFORCE_CLIENT_ID'
      client_secret: 'SALESFORCE_CLIENT_SECRET'
      destination:
        client_id: 'SALESFORCE_CLIENT_ID_DESTINATION'
        client_secret: 'SALESFORCE_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'SALESFORCE_CLIENT_ID_SOURCE'
        client_secret: 'SALESFORCE_CLIENT_SECRET_SOURCE'
    singer-salesforce:
      client_id: 'SINGER_SALESFORCE_CLIENT_ID'
      client_secret: 'SINGER_SALESFORCE_CLIENT_SECRET'
      destination:
        client_id: 'SINGER_SALESFORCE_CLIENT_ID_DESTINATION'
        client_secret: 'SINGER_SALESFORCE_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'SINGER_SALESFORCE_CLIENT_ID_SOURCE'
        client_secret: 'SINGER_SALESFORCE_CLIENT_SECRET_SOURCE'
    intercom:
      client_id: 'INTERCOM_CLIENT_ID'
      client_secret: 'INTERCOM_CLIENT_SECRET'
      destination:
        client_id: 'INTERCOM_CLIENT_ID_DESTINATION'
        client_secret: 'INTERCOM_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'INTERCOM_CLIENT_ID_SOURCE'
        client_secret: 'INTERCOM_CLIENT_SECRET_SOURCE'
    singer-intercom:
      client_id: 'SINGER_INTERCOM_CLIENT_ID'
      client_secret: 'SINGER_INTERCOM_CLIENT_SECRET'
      callback_url: 'SINGER_INTERCOM_CALLBACK_URL'
      destination:
        client_id: 'SINGER_INTERCOM_CLIENT_ID_DESTINATION'
        client_secret: 'SINGER_INTERCOM_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'SINGER_INTERCOM_CLIENT_ID_SOURCE'
        client_secret: 'SINGER_INTERCOM_CLIENT_SECRET_SOURCE'
        callback_url: 'SINGER_INTERCOM_CALLBACK_URL'
    stripe:
      client_id: STRIPE_CLIENT_ID
      client_secret: STRIPE_CLIENT_SECRET
    mailchimp:
      client_id: MAILCHIMP_CLIENT_ID
      client_secret: MAILCHIMP_CLIENT_SECRET
    facebook_ads:
      client_id: FACEBOOK_ADS_CLIENT_ID
      client_secret: FACEBOOK_ADS_CLIENT_SECRET
    singer-facebook-marketing:
      client_id: 'SINGER_FACEBOOK_MARKETING_CLIENT_ID'
      client_secret: 'SINGER_FACEBOOK_MARKETING_CLIENT_SECRET'
    linkedin_ads:
      client_id: LINKEDIN_ADS_CLIENT_ID
      client_secret: LINKEDIN_ADS_CLIENT_SECRET
      source:
        client_id: LINKEDIN_ADS_CLIENT_ID_SOURCE
        client_secret: LINKEDIN_ADS_CLIENT_SECRET_SOURCE
      destination:
        client_id: LINKEDIN_ADS_CLIENT_ID_DESTINATION
        client_secret: LINKEDIN_ADS_CLIENT_SECRET_DESTINATION
    linkedin_audience:
      client_id: LINKEDIN_AUDIENCE_CLIENT_ID
      client_secret: LINKEDIN_AUDIENCE_CLIENT_SECRET
      source:
        client_id: LINKEDIN_AUDIENCE_CLIENT_ID_SOURCE
        client_secret: LINKEDIN_AUDIENCE_CLIENT_SECRET_SOURCE
      destination:
        client_id: LINKEDIN_AUDIENCE_CLIENT_ID_DESTINATION
        client_secret: LINKEDIN_AUDIENCE_CLIENT_SECRET_DESTINATION
    quickbooks:
      consumer_key: QUICKBOOKS_CONSUMER_KEY
      consumer_secret: QUICKBOOKS_CONSUMER_SECRET
      client_id: QUICKBOOKS_CLIENT_ID
      client_secret: QUICKBOOKS_CLIENT_SECRET
    google_sheets:
      client_id: GOOGLE_SHEETS_CLIENT_ID
      client_secret: GOOGLE_SHEETS_CLIENT_SECRET
    google_analytics:
      client_id: GOOGLE_ANALYTICS_CLIENT_ID
      client_secret: GOOGLE_ANALYTICS_CLIENT_SECRET
      destination:
        client_id: 'GOOGLE_ANALYTICS_CLIENT_ID_DESTINATION'
        client_secret: 'GOOGLE_ANALYTICS_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'GOOGLE_ANALYTICS_CLIENT_ID_SOURCE'
        client_secret: 'GOOGLE_ANALYTICS_CLIENT_SECRET_SOURCE'
    google_analytics_4:
      client_id: GOOGLE_ANALYTICS_4_CLIENT_ID
      client_secret: GOOGLE_ANALYTICS_4_CLIENT_SECRET
      destination:
        client_id: GOOGLE_ANALYTICS_4_CLIENT_ID_DESTINATION
        client_secret: GOOGLE_ANALYTICS_4_CLIENT_SECRET_DESTINATION
      source:
        client_id: GOOGLE_ANALYTICS_4_CLIENT_ID_SOURCE
        client_secret: GOOGLE_ANALYTICS_4_CLIENT_SECRET_SOURCE
    google_adwords:
      client_id: GOOGLE_ADWORDS_CLIENT_ID
      client_secret: GOOGLE_ADWORDS_CLIENT_SECRET
      developer_token: GOOGLE_ADWORDS_DEVELOPER_TOKEN
    singer-google_adwords:
      client_id: SINGER_GOOGLE_ADWORDS_CLIENT_ID
      client_secret: SINGER_GOOGLE_ADWORDS_CLIENT_SECRET
      developer_token: SINGER_GOOGLE_ADWORDS_DEVELOPER_TOKEN
    singer-google-analytics:
      client_id: SINGER_GOOGLE_ANALYTICS_CLIENT_ID
      client_secret: SINGER_GOOGLE_ANALYTICS_CLIENT_SECRET
    singer-google-sheets:
      client_id: SINGER_GOOGLE_SHEETS_CLIENT_ID
      client_secret: SINGER_GOOGLE_SHEETS_CLIENT_SECRET
    singer-google-search-console:
      client_id: SINGER_GOOGLE_SEARCH_CONSOLE_CLIENT_ID
      client_secret: SINGER_GOOGLE_SEARCH_CONSOLE_CLIENT_SECRET
    singer-bing-ads:
      client_id: SINGER_BING_ADS_CLIENT_ID
      client_secret: SINGER_BING_ADS_CLIENT_SECRET
      developer_token: BING_DEVELOPER_TOKEN
    hubspot:
      client_id: HUBSPOT_CLIENT_ID
      client_secret: HUBSPOT_CLIENT_SECRET
    hubspot_crm:
      client_id: HUBSPOT_CRM_CLIENT_ID
      client_secret: HUBSPOT_CRM_CLIENT_SECRET
    shopify:
      client_id: SHOPIFY_CLIENT_ID
      client_secret: SHOPIFY_CLIENT_SECRET
    zendesk:
      client_id: ZENDESK_CLIENT_ID
      client_secret: ZENDESK_CLIENT_SECRET
    singer-zendesk-support:
      client_id: SINGER_ZENDESK_SUPPORT_CLIENT_ID
      client_secret: SINGER_ZENDESK_SUPPORT_CLIENT_SECRET
    singer-zendesk-chat:
      client_id: SINGER_ZENDESK_CHAT_CLIENT_ID
      client_secret: SINGER_ZENDESK_CHAT_CLIENT_SECRET
    zendesk_chat:
      client_id: ZENDESK_CHAT_CLIENT_ID
      client_secret: ZENDESK_CHAT_CLIENT_SECRET
    singer-pipedrive:
      client_id: SINGER_PIPEDRIVE_CLIENT_ID
      client_secret: SINGER_PIPEDRIVE_CLIENT_SECRET
    xero:
      client_id: XERO_CLIENT_ID
      client_secret: XERO_CLIENT_SECRET
    pardot:
      client_id: PARDOT_CLIENT_ID
      client_secret: PARDOT_CLIENT_SECRET
      callback_url: PARDOT_CALLBACK_URL
      destination:
        client_id: PARDOT_CLIENT_ID_DESTINATION
        client_secret: PARDOT_CLIENT_SECRET_DESTINATION
        callback_url: PARDOT_CLIENT_CALLBACK_URL_DESTINATION
      source:
        client_id: PARDOT_CLIENT_ID_SOURCE
        client_secret: PARDOT_CLIENT_SECRET_SOURCE
        callback_url: PARDOT_CLIENT_CALLBACK_URL_SOURCE
    google_adwords_remarketing_lists:
      client_id: 'GOOGLE_ADWORDS_REMARKETING_LISTS_CLIENT_ID'
      client_secret: 'GOOGLE_ADWORDS_REMARKETING_LISTS_CLIENT_SECRET'
      developer_token: 'GOOGLE_ADWORDS_REMARKETING_LISTS_DEVELOPER_TOKEN'
      destination:
        client_id: 'GOOGLE_ADWORDS_REMARKETING_LISTS_CLIENT_ID_DESTINATION'
        client_secret: 'GOOGLE_ADWORDS_REMARKETING_LISTS_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'GOOGLE_ADWORDS_REMARKETING_LISTS_CLIENT_ID_SOURCE'
        client_secret: 'GOOGLE_ADWORDS_REMARKETING_LISTS_CLIENT_SECRET_SOURCE'
    google_adwords_remarketing_lists_v1:
      client_id: 'GOOGLE_ADWORDS_REMARKETING_LISTS_CLIENT_ID_v1'
      client_secret: 'GOOGLE_ADWORDS_REMARKETING_LISTS_CLIENT_SECRET_v1'
      developer_token: 'GOOGLE_ADWORDS_REMARKETING_LISTS_DEVELOPER_TOKEN_v1'
      destination:
        client_id: 'GOOGLE_ADWORDS_REMARKETING_LISTS_CLIENT_ID_DESTINATION_v1'
        client_secret: 'GOOGLE_ADWORDS_REMARKETING_LISTS_CLIENT_SECRET_DESTINATION_v1'
      source:
        client_id: 'GOOGLE_ADWORDS_REMARKETING_LISTS_CLIENT_ID_SOURCE_v1'
        client_secret: 'GOOGLE_ADWORDS_REMARKETING_LISTS_CLIENT_SECRET_SOURCE_v1'
    google_adwords_enhanced_conversions:
      client_id: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_CLIENT_ID'
      client_secret: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_CLIENT_SECRET'
      developer_token: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_DEVELOPER_TOKEN'
      destination:
        client_id: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_CLIENT_ID_DESTINATION'
        client_secret: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_CLIENT_ID_SOURCE'
        client_secret: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_CLIENT_SECRET_SOURCE'
    google_adwords_offline_conversions:
      client_id: 'GOOGLE_ADWORDS_OFFLINE_CONVERSIONS_CLIENT_ID'
      client_secret: 'GOOGLE_ADWORDS_OFFLINE_CONVERSIONS_CLIENT_SECRET'
      developer_token: 'GOOGLE_ADWORDS_OFFLINE_CONVERSIONS_DEVELOPER_TOKEN'
      destination:
        client_id: 'GOOGLE_ADWORDS_OFFLINE_CONVERSIONS_CLIENT_ID_DESTINATION'
        client_secret: 'GOOGLE_ADWORDS_OFFLINE_CONVERSIONS_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'GOOGLE_ADWORDS_OFFLINE_CONVERSIONS_CLIENT_ID_SOURCE'
        client_secret: 'GOOGLE_ADWORDS_OFFLINE_CONVERSIONS_CLIENT_SECRET_SOURCE'
    google_adwords_enhanced_conversions_v1:
      client_id: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_CLIENT_ID_v1'
      client_secret: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_CLIENT_SECRET_v1'
      developer_token: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_DEVELOPER_TOKEN_v1'
      destination:
        client_id: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_CLIENT_ID_DESTINATION_v1'
        client_secret: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_CLIENT_SECRET_DESTINATION_v1'
      source:
        client_id: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_CLIENT_ID_SOURCE_v1'
        client_secret: 'GOOGLE_ADWORDS_ENHANCED_CONVERSIONS_CLIENT_SECRET_SOURCE_v1'
    snapchat_conversion:
      client_id: 'SNAPCHAT_CONVERSION_CLIENT_ID'
      client_secret: 'SNAPCHAT_CONVERSION_CLIENT_SECRET'
      destination:
        client_id: 'SNAPCHAT_CONVERSION_CLIENT_ID_DESTINATION'
        client_secret: 'SNAPCHAT_CONVERSION_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'SNAPCHAT_CONVERSION_CLIENT_ID_SOURCE'
        client_secret: 'SNAPCHAT_CONVERSION_CLIENT_SECRET_SOURCE'
    snapchat_custom_audience:
      client_id: 'SNAPCHAT_CUSTOM_AUDIENCE_CLIENT_ID'
      client_secret: 'SNAPCHAT_CUSTOM_AUDIENCE_CLIENT_SECRET'
      destination:
        client_id: 'SNAPCHAT_CUSTOM_AUDIENCE_CLIENT_ID_DESTINATION'
        client_secret: 'SNAPCHAT_CUSTOM_AUDIENCE_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'SNAPCHAT_CUSTOM_AUDIENCE_CLIENT_ID_SOURCE'
        client_secret: 'SNAPCHAT_CUSTOM_AUDIENCE_CLIENT_SECRET_SOURCE'
    campaign_manager:
      client_id: 'CAMPAIGN_MANAGER_CLIENT_ID'
      client_secret: 'CAMPAIGN_MANAGER_CLIENT_SECRET'
      developer_token: 'CAMPAIGN_MANAGER_DEVELOPER_TOKEN'
      destination:
        client_id: 'CAMPAIGN_MANAGER_CLIENT_ID_DESTINATION'
        client_secret: 'CAMPAIGN_MANAGER_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'CAMPAIGN_MANAGER_CLIENT_ID_SOURCE'
        client_secret: 'CAMPAIGN_MANAGER_CLIENT_SECRET_SOURCE'
    criteo_audience:
      client_id: 'CRITEO_AUDIENCE_CLIENT_ID'
      client_secret: 'CRITEO_AUDIENCE_CLIENT_SECRET'
      destination:
        client_id: 'CRITEO_AUDIENCE_CLIENT_ID_DESTINATION'
        client_secret: 'CRITEO_AUDIENCE_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'CRITEO_AUDIENCE_CLIENT_ID_SOURCE'
        client_secret: 'CRITEO_AUDIENCE_CLIENT_SECRET_SOURCE'
    twitter_ads:
      consumer_key: 'TWITTER_ADS_CONSUMER_KEY'
      consumer_secret: 'TWITTER_ADS_CONSUMER_SECRET'
      session_secret: 'TWITTER_ADS_SESSION_SECRET'
      secure_cookie: 'TWITTER_ADS_SECURE_COOKIE'
      skip_extended_user_profile: 'TWITTER_ADS_SKIP_EXTENDED_USER_PROFILE'
      destination:
        consumer_key: 'TWITTER_ADS_CONSUMER_KEY_DESTINATION'
        consumer_secret: 'TWITTER_ADS_CONSUMER_SECRET_DESTINATION'
        skip_extended_user_profile: 'TWITTER_ADS_SKIP_EXTENDED_USER_PROFILE_DESTINATION'
      source:
        consumer_key: 'TWITTER_ADS_CONSUMER_KEY_SOURCE'
        consumer_secret: 'TWITTER_ADS_CONSUMER_SECRET_SOURCE'
        skip_extended_user_profile: 'TWITTER_ADS_SKIP_EXTENDED_USER_PROFILE_SOURCE'
    x_audience:
      consumer_key: 'TWITTER_ADS_CONSUMER_KEY'
      consumer_secret: 'TWITTER_ADS_CONSUMER_SECRET'
      session_secret: 'TWITTER_ADS_SESSION_SECRET'
      secure_cookie: 'TWITTER_ADS_SECURE_COOKIE'
      skip_extended_user_profile: 'TWITTER_ADS_SKIP_EXTENDED_USER_PROFILE'
      destination:
        consumer_key: 'TWITTER_ADS_CONSUMER_KEY_DESTINATION'
        consumer_secret: 'TWITTER_ADS_CONSUMER_SECRET_DESTINATION'
        skip_extended_user_profile: 'TWITTER_ADS_SKIP_EXTENDED_USER_PROFILE_DESTINATION'
      source:
        consumer_key: 'TWITTER_ADS_CONSUMER_KEY_SOURCE'
        consumer_secret: 'TWITTER_ADS_CONSUMER_SECRET_SOURCE'
        skip_extended_user_profile: 'TWITTER_ADS_SKIP_EXTENDED_USER_PROFILE_SOURCE'
    amazon_audience:
      client_id: 'AMAZON_AUDIENCE_CLIENT_ID'
      client_secret: 'AMAZON_AUDIENCE_CLIENT_SECRET'
      destination:
        client_id: 'AMAZON_AUDIENCE_CLIENT_ID_DESTINATION'
        client_secret: 'AMAZON_AUDIENCE_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'AMAZON_AUDIENCE_CLIENT_ID_SOURCE'
        client_secret: 'AMAZON_AUDIENCE_CLIENT_SECRET_SOURCE'
    bingads_audience:
      client_id: 'BINGADS_AUDIENCE_CLIENT_ID'
      client_secret: 'BINGADS_AUDIENCE_CLIENT_SECRET'
      developer_token: 'BING_DEVELOPER_TOKEN'
      destination:
        client_id: 'BINGADS_AUDIENCE_CLIENT_ID_DESTINATION'
        client_secret: 'BINGADS_AUDIENCE_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'BINGADS_AUDIENCE_CLIENT_ID_SOURCE'
        client_secret: 'BINGADS_AUDIENCE_CLIENT_SECRET_SOURCE'
    bingads_offline_conversions:
      client_id: 'BINGADS_AUDIENCE_CLIENT_ID'
      client_secret: 'BINGADS_AUDIENCE_CLIENT_SECRET'
      developer_token: 'BING_DEVELOPER_TOKEN'
      destination:
        client_id: 'BINGADS_AUDIENCE_CLIENT_ID_DESTINATION'
        client_secret: 'BINGADS_AUDIENCE_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'BINGADS_AUDIENCE_CLIENT_ID_SOURCE'
        client_secret: 'BINGADS_AUDIENCE_CLIENT_SECRET_SOURCE'
    tiktok_audience:
      client_id: 'TIKTOK_AUDIENCE_CLIENT_ID'
      client_secret: 'TIKTOK_AUDIENCE_CLIENT_SECRET'
      destination:
        client_id: 'TIKTOK_AUDIENCE_CLIENT_ID_DESTINATION'
        client_secret: 'TIKTOK_AUDIENCE_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'TIKTOK_AUDIENCE_CLIENT_ID_SOURCE'
        client_secret: 'TIKTOK_AUDIENCE_CLIENT_SECRET_SOURCE'
    reddit:
      client_id: 'REDDIT_CLIENT_ID'
      client_secret: 'REDDIT_CLIENT_SECRET'
      destination:
        client_id: 'REDDIT_CLIENT_ID_DESTINATION'
        client_secret: 'REDDIT_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'REDDIT_CLIENT_ID_SOURCE'
        client_secret: 'REDDIT_CLIENT_SECRET_SOURCE'
    yandex_metrica_offline_events:
      client_id: 'YANDEX_METRICA_OFFLINE_EVENTS_CLIENT_ID'
      client_secret: 'YANDEX_METRICA_OFFLINE_EVENTS_CLIENT_SECRET'
      destination:
        client_id: 'YANDEX_METRICA_OFFLINE_EVENTS_CLIENT_ID_DESTINATION'
        client_secret: 'YANDEX_METRICA_OFFLINE_EVENTS_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'YANDEX_METRICA_OFFLINE_EVENTS_CLIENT_ID_SOURCE'
        client_secret: 'YANDEX_METRICA_OFFLINE_EVENTS_CLIENT_SECRET_SOURCE'
    zoho:
      client_id: 'ZOHO_CLIENT_ID'
      client_secret: 'ZOHO_CLIENT_SECRET'
      destination:
        client_id: 'ZOHO_CLIENT_ID_DESTINATION'
        client_secret: 'ZOHO_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'ZOHO_CLIENT_ID_SOURCE'
        client_secret: 'ZOHO_CLIENT_SECRET_SOURCE'
    zoho_dev:
      client_id: 'ZOHO_DEV_CLIENT_ID'
      client_secret: 'ZOHO_DEV_CLIENT_SECRET'
      destination:
        client_id: 'ZOHO_DEV_CLIENT_ID_DESTINATION'
        client_secret: 'ZOHO_DEV_CLIENT_SECRET_DESTINATION'
      source:
        client_id: 'ZOHO_DEV_CLIENT_ID_SOURCE'
        client_secret: 'ZOHO_DEV_CLIENT_SECRET_SOURCE'
