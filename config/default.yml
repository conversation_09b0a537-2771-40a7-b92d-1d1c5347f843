host: 0.0.0.0
port: 3033

oauth:
  base_redirect_url: 'http://localhost:3000'
  credentials:
    google_search_console:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    google:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    google_reviews:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    bing_ads:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    google_dfp:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    bigquery:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    salesforce:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    singer-salesforce:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    salesforce_sandbox:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    intercom:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    singer-intercom:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    stripe:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    mailchimp:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    facebook_ads:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    singer-facebook-marketing:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    linkedin_ads:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    linkedin_audience:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    quickbooks:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    google_sheets:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    google_analytics:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    google_analytics_4:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    google_adwords:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      developer_token: 'the-developer-token'
    singer-google_adwords:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      developer_token: 'the-developer-token'
    singer-google-analytics:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    singer-google-sheets:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    singer-google-search-console:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    singer-bing-ads:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    singer-pipedrive:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    hubspot:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    hubspot_crm:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    shopify:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    zendesk:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    singer-zendesk-support:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    singer-zendesk-chat:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    zendesk_chat:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    xero:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
    pardot:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    google_adwords_remarketing_lists:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      developer_token: 'the-developer-token'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    google_adwords_remarketing_lists_v1:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      developer_token: 'the-developer-token'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    google_adwords_enhanced_conversions:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      developer_token: 'the-developer-token'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    google_adwords_enhanced_conversions_v1:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      developer_token: 'the-developer-token'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    google_adwords_offline_conversions:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      developer_token: 'the-developer-token'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    snapchat_conversion:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    snapchat_custom_audience:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    campaign_manager:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      developer_token: 'the-developer-token'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    criteo_audience:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    amazon_audience:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    twitter_ads:
      consumer_key: 'the-consumer-key'
      consumer_secret: 'the-consumer-secret'
      session_secret: 'random secret'
      destination:
        skip_extended_user_profile: true
        consumer_key: 'the-consumer-key'
        consumer_secret: 'the-consumer-secret'
      source:
        skip_extended_user_profile: true
        consumer_key: 'the-consumer-key'
        consumer_secret: 'the-consumer-secret'
    x_audience:
      consumer_key: 'the-consumer-key'
      consumer_secret: 'the-consumer-secret'
      session_secret: 'random secret'
      destination:
        skip_extended_user_profile: true
        consumer_key: 'the-consumer-key'
        consumer_secret: 'the-consumer-secret'
      source:
        skip_extended_user_profile: true
        consumer_key: 'the-consumer-key'
        consumer_secret: 'the-consumer-secret'
    bingads_audience:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      developer_token: 'the-developer-token'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    bingads_offline_conversions:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      developer_token: 'the-developer-token'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    tiktok_audience:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    reddit:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    yandex_metrica_offline_events:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    zoho:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
    zoho_dev:
      client_id: 'the-client-id'
      client_secret: 'the-client-secret'
      destination:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
      source:
        client_id: 'the-client-id'
        client_secret: 'the-client-secret'
